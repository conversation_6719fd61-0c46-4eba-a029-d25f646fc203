<template>
    <table class="fs-xs w-full text-left text-text-headings">
        <tbody>
            <tr
                v-for="(item, index) in tableRows"
                :key="index"
                class="border-t-4 border-white bg-surface-page"
                :class="[item.isHighlighted ? 'rounded' : '']"
            >
                <td
                    :class="[
                        'w-[246px] px-16 py-12 font-header font-bold',
                        item.isHighlighted
                            ? 'rounded bg-surface-action-3x-light'
                            : 'bg-surface-grey-2x-light',
                    ]"
                >
                    {{ item.label }}
                </td>
                <td
                    class="fs-md relative w-[246px] px-16 py-12 after:absolute after:bottom-1 after:left-0 after:h-1 after:w-full after:bg-border-primary-light"
                    :class="
                        item.isHighlighted
                            ? 'font-header font-bold'
                            : 'font-normal'
                    "
                >
                    <span class="flex text-text-grey items-center justify-between">
                        <!-- Display icons for social media channels -->
                        <div v-if="item.key === 'social_media'" class="flex items-center gap-8">
                            <component
                                v-for="(platform, index) in getSocialMediaPlatforms(tableData[item.key])"
                                :key="index"
                                :is="getSocialMediaIcon(platform)"
                                class="h-24 w-24"
                            />
                        </div>
                        <!-- Display text for other rows -->
                        <span v-else>{{ tableData[item.key] }}</span>

                        <IconEdit v-if="!item.isHighlighted" />
                    </span>
                </td>
            </tr>
        </tbody>
    </table>
</template>

<script setup>
// Import social media icon components
import FacebookIcon from '@/Components/SocialButtons/FacebookIcon.vue'
import InstagramIcon from '@/Components/SocialButtons/InstagramIcon.vue'
import TwitterIcon from '@/Components/SocialButtons/TwitterIcon.vue'
import TikTokIcon from '@/Components/SocialButtons/TikTokIcon.vue'
import YouTubeIcon from '@/Components/SocialButtons/YouTubeIcon.vue'
import LinkedInIcon from '@/Components/SocialButtons/LinkedInIcon.vue'
import PinterestIcon from '@/Components/SocialButtons/PinterestIcon.vue'
import SnapchatIcon from '@/Components/SocialButtons/SnapchatIcon.vue'
import IconEdit from '@/Components/Icons/IconEdit.vue'

const props = defineProps({
    tableData: { type: Object, required: true, default: () => ({}) },
});

// Define the table rows with labels, keys, and a flag for highlighted rows
const tableRows = [
    { label: "Social media channels this person uses", key: "social_media" },
    { label: "Communication channels this person uses", key: "communication" },
    { label: "Other causes they support", key: "other_causes" },
    // { label: "Brands they engage with", key: "brands" },
    { label: "Fundraising campaigns they respond to", key: "fundraising" },
];

// Function to parse social media platforms from comma-separated string
const getSocialMediaPlatforms = (socialMediaString) => {
    if (!socialMediaString || typeof socialMediaString !== 'string') {
        return [];
    }
    return socialMediaString.split(',').map(platform => platform.trim()).filter(platform => platform);
};

// Function to get the appropriate icon component for each platform
const getSocialMediaIcon = (platform) => {
    const platformLower = platform.toLowerCase();

    const iconMap = {
        'facebook': FacebookIcon,
        'instagram': InstagramIcon,
        'twitter': TwitterIcon,
        'x': TwitterIcon,
        'tiktok': TikTokIcon,
        'youtube': YouTubeIcon,
        'linkedin': LinkedInIcon,
        'pinterest': PinterestIcon,
        'snapchat': SnapchatIcon,
    };

    return iconMap[platformLower] || FacebookIcon; // Default fallback
};
</script>
