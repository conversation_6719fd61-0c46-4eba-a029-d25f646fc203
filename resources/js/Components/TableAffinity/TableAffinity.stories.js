import TableAffinity from "./TableAffinity.vue";

export default {
    component: TableAffinity,
    tags: ["autodocs"],
    args: {},
};

export const Persona1 = {
    args: {
        tableData: {
            social_media: "Instagram, TikTok",
            communication: "Email, Social Media DMs",
            fundraising: "Peer-to-peer, Challenge Events",
        },
    },
    // Adding methods to handle the emitted events
    argTypes: {
        action: { action: "clicked" }, // This will allow Storybook to log actions
    },
};

export const WithMultipleSocialPlatforms = {
    args: {
        tableData: {
            social_media: "Facebook, Instagram, Twitter, YouTube, LinkedIn",
            communication: "Email, Social Media DMs, WhatsApp",
            other_causes: "Environmental, Education",
            fundraising: "Peer-to-peer, Challenge Events, Online Campaigns",
        },
    },
};

export const WithDifferentPlatforms = {
    args: {
        tableData: {
            social_media: "TikTok, Snapchat, Pinterest",
            communication: "SMS, Phone Call",
            other_causes: "Animal Welfare",
            fundraising: "Direct Mail, Events",
        },
    },
};
