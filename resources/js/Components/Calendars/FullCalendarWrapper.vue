<template>
    <div class="calendar-wrapper">
        <div
            class="calendar-header mb-40 flex flex-col items-center justify-between gap-16 md:flex-row md:gap-8"
        >
            <NavPills
                :options="viewLabels"
                :selectedIndex="selectedView"
                @update:selectedIndex="changeView"
                class="inline-flex md:order-2"
            />
            <div class="flex flex-col items-center gap-4 md:flex-row">
                <span class="fs-3xl xl:fs-6xl font-header md:order-1">{{
                    currentTitle
                }}</span>
                <div class="flex items-center gap-4">
                    <button @click="goPrev" class="p-12">
                        <IconChevronLeft />
                        <span class="sr-only">Previous period</span>
                    </button>
                    <button @click="goNext" class="p-12">
                        <IconChevronRight />
                        <span class="sr-only">Next period</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- FullCalendar Instance -->
        <FullCalendar
            ref="calendarRef"
            class="min-h-[500px]"
            :options="calendarOptions"
        >
            <!-- Slot for custom event rendering -->
            <template #eventContent="{ event, timeText }">
                <PostEvent
                    :text="event.extendedProps.text"
                    :thumbnail="event.extendedProps.thumbnail"
                    :platform="event.extendedProps.platform"
                    :time="timeText"
                    @delete="handleDeleteEvent(event.id)"
                />
            </template>
        </FullCalendar>
    </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import FullCalendar from "@fullcalendar/vue3";
import dayGridPlugin from "@fullcalendar/daygrid";
import timeGridPlugin from "@fullcalendar/timegrid";
import listPlugin from "@fullcalendar/list";
import NavPills from "@/Components/NavPills/NavPills.vue";

import IconChevronLeft from "@/Components/Icons/IconChevronLeft.vue";
import IconChevronRight from "@/Components/Icons/IconChevronRight.vue";
import PostEvent from "./PostEvent.vue";

// == FullCalendar setup ==
const calendarRef = ref(null);
const calendarOptions = {
    plugins: [dayGridPlugin, timeGridPlugin, listPlugin],
    initialView: "dayGridMonth",
    eventTimeFormat: {
        hour: "2-digit",
        minute: "2-digit",
        hour12: false,
        meridiem: false,
    },
    locale: "en-GB",
    listDayFormat: {
        day: "numeric",
        weekday: "short",
    },
    listDaySideFormat: false,

    events: [
        // PLACEHOLDER DATA
        // TODO: Replace with actual scheduled posts data from the server or Ayrshare API
        {
            id: "1",
            title: "Post 1",
            start: "2025-06-22T10:00:00",
            text: "Exciting new product launch today!",
            // thumbnail: "/images/product.jpg", // placeholder path for post image
            platform: "Instagram",
        },
        {
            id: "2",
            title: "Post 2",
            start: "2025-06-23T14:00:00",
            text: "Check out our latest blog post!",
            // thumbnail: "/images/blog.jpg", // placeholder path for post image
            platform: "Facebook",
        },
        {
            id: "3",
            title: "Post 3",
            start: "2025-06-24T09:00:00",
            text: "Join our webinar on Vue.js best practices.",
            // thumbnail: "/images/webinar.jpg", // placeholder path for post image
            platform: "LinkedIn",
        },
    ],
    headerToolbar: false,
};

// == Custom view modes ==
// For the calendar toggle handling
const viewModes = [
    { name: "dayGridMonth", label: "Monthly" },
    { name: "timeGridWeek", label: "Weekly" },
    { name: "listWeek", label: "List" },
];
const viewLabels = viewModes.map((view) => view.label);

const selectedView = ref(0);

// == Navigation methods ==
const currentTitle = ref("");

const updateCurrentTitle = () => {
    const calendarApi = calendarRef.value?.getApi();
    if (calendarApi) {
        currentTitle.value = calendarApi.view.title;
    }
};

const changeView = (index) => {
    selectedView.value = index;

    const calendarApi = calendarRef.value?.getApi();
    if (calendarApi) {
        calendarApi.changeView(viewModes[index].name);
        updateCurrentTitle();
    }
};

const goPrev = () => {
    const calendarApi = calendarRef.value?.getApi();
    if (calendarApi) {
        calendarApi.prev();
        updateCurrentTitle();
    }
};

const goNext = () => {
    const calendarApi = calendarRef.value?.getApi();
    if (calendarApi) {
        calendarApi.next();
        updateCurrentTitle();
    }
};

onMounted(() => {
    updateCurrentTitle();
});

// == Event handling ==
const handleDeleteEvent = (eventId) => {
    // TODO: Implement event deletion logic
    console.log(`Delete event with ID: ${eventId}`);
};
</script>

<style lang="postcss">
.fc .fc-toolbar-title {
    @apply text-6xl lg:text-6xl-L xl:text-6xl-XL;
}

.fc-theme-standard th {
    @apply border-0;
}

.fc-dayGridMonth-view td {
    @apply border-0;
}

.fc-daygrid-day {
    @apply border-8 border-white bg-surface-grey-2x-light !important;
}

.fc-theme-standard .fc-scrollgrid {
    border: none;
}

.fc-daygrid-day-number {
    @apply text-md font-bold lg:text-md-L xl:text-md-XL;
}

.fc-daygrid-day-top {
    @apply pt-8;
}

.fc-daygrid-day-top {
    padding-left: 4px;
    padding-right: 4px;
}

.fc-scroller-harness {
    @apply mb-12;
}

.fc-v-event {
    @apply border-none bg-transparent;
}

.fc-timegrid-body .post-event {
    @apply bg-surface-grey-2x-light;
}

.fc-list-table .post-event {
    height: 120px;
    @apply rounded-xl text-sm shadow-md lg:text-sm-L xl:text-sm-XL;

    .post-event__text-wrapper {
        @apply px-8 py-12;
    }

    .post-event__social-icon {
        @apply size-28;
    }

    .post-event__close-icon {
        @apply size-28;
    }
}
.fc-list-event-graphic {
    display: none;
}

.fc-list-event-time {
    @apply font-bold text-text-grey;
}

.fc-list-day-text {
    @apply font-header font-normal md:text-2xl lg:text-2xl-L xl:text-2xl-XL;
}
</style>
