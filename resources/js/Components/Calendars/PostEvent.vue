<template>
    <div
        class="post-event flex h-[50px] max-w-full overflow-hidden bg-white text-[8px]"
    >
        <!-- Thumbnail -->
        <img
            :src="thumbnail"
            alt="Post thumbnail"
            class="h-full w-[30%] object-cover"
        />

        <!-- Content -->
        <div
            class="post-event__text-wrapper flex min-w-0 flex-1 flex-col justify-between p-4"
        >
            <!-- Top Row: Text + Delete -->
            <div class="flex items-start justify-between gap-2 text-text-body">
                <div class="min-w-0 truncate" :title="text">
                    {{ truncatedText }}
                </div>
                <button @click="$emit('delete')">
                    <IconClose class="post-event__close-icon" />
                    <span class="sr-only">Delete Post</span>
                </button>
            </div>

            <!-- Bottom Row: Platform + Time -->
            <div class="flex justify-between">
                <div>
                    <component
                        :is="getIconComponent(platform)"
                        class="post-event__social-icon size-20 stroke-black"
                    />
                    <span class="sr-only">{{ platform }}</span>
                </div>
                <div class="font-bold text-text-grey">{{ time }}</div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed } from "vue";
import IconClose from "../Icons/IconClose.vue";
import fallbackImage from "../../../images/postevent-thumbnail-default.jpg";

const props = defineProps({
    text: { type: String, required: true },
    thumbnail: { type: String, required: true },
    platform: { type: String, required: true },
    time: { type: String, required: true },
    maxChars: { type: Number, default: 40 }, // truncate after 40 chars by default
});

const truncatedText = computed(() => {
    return props.text.length > props.maxChars
        ? props.text.slice(0, props.maxChars) + "..."
        : props.text;
});

// Fallback image if thumbnail is not provided
const thumbnail = computed(() => {
    return props.thumbnail || fallbackImage;
});

defineEmits(["delete"]);

// Social platform icon mapping
import FacebookIcon from "../SocialButtons/FacebookIcon.vue";
import InstagramIcon from "../SocialButtons/InstagramIcon.vue";
import LinkedInIcon from "../SocialButtons/LinkedInIcon.vue";
import PinterestIcon from "../SocialButtons/PinterestIcon.vue";
import SnapchatIcon from "../SocialButtons/SnapchatIcon.vue";
import TikTokIcon from "../SocialButtons/TikTokIcon.vue";
import WhatsAppIcon from "../SocialButtons/WhatsAppIcon.vue";
import YouTubeIcon from "../SocialButtons/YouTubeIcon.vue";
import TwitterIcon from "../SocialButtons/TwitterIcon.vue";

const iconMapping = {
    Facebook: FacebookIcon,
    Instagram: InstagramIcon,
    LinkedIn: LinkedInIcon,
    Pinterest: PinterestIcon,
    Snapchat: SnapchatIcon,
    TikTok: TikTokIcon,
    WhatsApp: WhatsAppIcon,
    YouTube: YouTubeIcon,
    Twitter: TwitterIcon,
};

const getIconComponent = (label) => {
    return iconMapping[label] || null;
};
</script>
