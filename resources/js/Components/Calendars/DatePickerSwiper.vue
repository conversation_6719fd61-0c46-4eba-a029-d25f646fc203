<template>
    <div class="flex flex-col items-center space-y-12">
        <!-- Month Title -->
        <div class="fs-xl text-center font-header font-light uppercase">
            {{ currentMonth }}
        </div>

        <div class="flex items-center space-x-2">
            <button @click="slidePrev" class="p-8">
                <IconChevronLeft />
            </button>

            <Swiper
                ref="swiperRef"
                :modules="[Navigation]"
                :slides-per-view="5"
                :breakpoints="{
                    500: {
                        spaceBetween: 10,
                    },
                }"
                :space-between="5"
                class="w-full max-w-[300px] sm:max-w-md lg:max-w-xl"
                :initial-slide="middleIndex"
            >
                <SwiperSlide v-for="(date, index) in dateList" :key="index">
                    <div
                        @click="selectDate(index)"
                        :class="[
                            'cursor-pointer rounded-lg border p-8 text-center',
                            selectedIndex === index
                                ? 'border-border-action bg-surface-action-3x-light'
                                : 'border-border-primary bg-white',
                        ]"
                    >
                        <p class="fs-7xl mb-8 font-header font-light">
                            {{ date.date }}
                        </p>

                        <p class="fs-xs font-extrabold uppercase">
                            {{ date.day }}
                        </p>
                    </div>
                </SwiperSlide>
            </Swiper>

            <button @click="slideNext" class="p-8">
                <IconChevronRight />
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from "vue";
import { startOfDay, isBefore, addDays, format } from "date-fns";
import { Swiper, SwiperSlide } from "swiper/vue";
import { Navigation } from "swiper/modules";
import "swiper/css";
import "swiper/css/navigation";
import IconChevronRight from "../Icons/IconChevronRight.vue";
import IconChevronLeft from "../Icons/IconChevronLeft.vue";

const props = defineProps({
    modelValue: {
        type: String,
        required: true,
    },
});

const daysToShow = 15;
const middleIndex = Math.floor(daysToShow / 2) + 5;

const baseDate = ref(new Date());
const selectedIndex = ref(middleIndex);
const swiperRef = ref(null);

const generateDates = () => {
    return Array.from({ length: daysToShow }, (_, i) => {
        const date = addDays(baseDate.value, i - middleIndex);
        return {
            day: format(date, "EEE"), // Mon, Tue…
            date: format(date, "d"), // 1, 2, …31
            month: format(date, "LLLL"), // January, February…
            fullDate: format(date, "yyyy-MM-dd"),
        };
    });
};
const dateList = ref(generateDates());

// Compute month based on the selected (center) date
const currentMonth = computed(() => {
    return dateList.value[selectedIndex.value]?.month.toUpperCase() || "";
});

// Go prev and reset Swiper to middle
const slidePrev = () => {
    baseDate.value.setDate(baseDate.value.getDate() - 1);
    dateList.value = generateDates();
    selectedIndex.value = middleIndex;
};

// Go next and reset Swiper to middle
const slideNext = () => {
    baseDate.value.setDate(baseDate.value.getDate() + 1);
    dateList.value = generateDates();
    selectedIndex.value = middleIndex;
};

// Handle user clicking a date
const selectDate = (index) => {
    selectedIndex.value = index;
};

const selectedDate = computed(() => {
    return dateList.value[selectedIndex.value].fullDate;
});

watch(
    () => props.modelValue,
    (newVal) => {
        const newDate = new Date(newVal);
        const today = startOfDay(new Date());

        // Ensure newDate is not in the past
        baseDate.value = isBefore(newDate, today) ? today : newDate;

        // Regenerate list and reset index
        dateList.value = generateDates();

        // Find the index in the list that matches the newDate
        const index = dateList.value.findIndex(
            (d) => d.fullDate === format(baseDate.value, "yyyy-MM-dd"),
        );
        selectedIndex.value = index !== -1 ? index : middleIndex;
    },
    { immediate: true },
);

const emit = defineEmits(["update:modelValue"]);

watch(selectedDate, (newVal) => {
    emit("update:modelValue", newVal);
});
</script>
