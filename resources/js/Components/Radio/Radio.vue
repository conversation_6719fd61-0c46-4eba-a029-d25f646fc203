<template>
    <div
        :class="{
            'pb-12': helperText || displayedErrorMessage,
            'pb-28 md:pb-32': !helperText && !displayedErrorMessage,
        }"
    >
        <div
            class="fs-md flex flex-wrap justify-center"
            :class="columnLayout ? 'flex-col gap-0' : 'gap-24 md:gap-32'"
        >
            <div
                v-for="option in options"
                :key="option.id"
                class="flex items-center"
            >
                <input
                    type="radio"
                    :id="`${id}_${option.id}`"
                    :value="option.value"
                    :name="id"
                    :checked="modelValue === option.value"
                    @change="$emit('update:modelValue', option.value)"
                    tabindex="-1"
                    class="focus:ring-blue-500 fs-md size-20 cursor-pointer text-text-action focus:ring-2 focus:ring-text-action-hover"
                    @blur="validateInput"
                />
                <label
                    :for="`${id}_${option.id}`"
                    class="cursor-pointer p-8"
                    @keydown="(event) => handleKeyPress(event, option)"
                    tabindex="0"
                >
                    <Tooltip
                        v-if="option.tooltip"
                        :label="option.name"
                        :tooltip="option.tooltip"
                    />

                    <template v-else>
                        {{ option.name }}
                    </template>
                </label>
            </div>
        </div>
        <p
            v-if="helperText || displayedErrorMessage"
            class="fs-xs mt-4 text-center"
            :class="{
                'text-text-error': hasError,
                'text-text-grey': !hasError,
            }"
        >
            {{ hasError ? displayedErrorMessage : helperText }}
        </p>
    </div>
</template>

<script setup>
import { ref, computed } from "vue";
import Tooltip from "../Tooltip/Tooltip.vue";

const props = defineProps({
    id: String,
    modelValue: String,
    options: { type: Array, required: true },
    required: Boolean,
    helperText: String,
    serverError: String,
    rules: {
        type: Array,
        default: () => [],
    },
    columnLayout: {
        Type: Boolean,
        Default: false,
    },
});

// V-Model handling
// ========================
const emit = defineEmits(["update:modelValue"]);

const handleKeyPress = (event, option) => {
    if (event.key === "Enter" || event.key === " ") {
        event.preventDefault();
        emit("update:modelValue", option.value);
    }
};

// Validation handling
// ========================
// Validate input on change
const errorMessage = ref("");
const hasError = computed(() => !!props.serverError || !!errorMessage.value);

// Client side validation
const validateInput = () => {
    errorMessage.value = "";
    for (const rule of props.rules) {
        const validationResult = rule(internalValue.value);
        if (validationResult !== true) {
            errorMessage.value = validationResult;
            break;
        }
    }
};

// This computed property decides which error message to display
// (server error overrides client)
const displayedErrorMessage = computed(() => {
    return props.serverError || errorMessage.value;
});
</script>
