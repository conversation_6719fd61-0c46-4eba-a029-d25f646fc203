<template>
    <header
        class="absolute left-0 right-0 top-0 flex h-64 w-full items-center justify-between bg-surface-page p-16 md:hidden"
    >
        <NavbarItem
            class="z-50 bg-surface-page"
            :showFull="showFull"
            :isLink="false"
            @click="showFull = !showFull"
            :isListItem="false"
        >
            <template #icon>
                <IconSidebar />
            </template>
            <span class="sr-only">Toggle Sidebar Menu</span>
        </NavbarItem>

        <IconLogoText class="w-88" />
    </header>
    <aside
        id="default-sidebar"
        class="absolute bottom-0 top-0 z-40 h-screen w-[190px] shrink-0 overflow-hidden overflow-y-auto bg-surface-page p-16 transition-all md:static md:translate-x-0 md:px-16 md:py-40"
        :class="
            showFull
                ? 'left-0 md:w-[210px] lg:w-[275px]'
                : '-left-full md:w-[88px]'
        "
        aria-label="Sidenav"
    >
        <div class="flex h-full flex-col items-start justify-start">
            <LogoNavbar :showFull="showFull" class="mb-24 hidden md:block" />

            <ul
                class="mb-auto w-full space-y-4 border-border-primary pb-8 pt-40 md:border-t md:pt-8"
            >
                <NavbarItem
                    class="hidden md:block"
                    :showFull="showFull"
                    :isLink="false"
                    @click="showFull = !showFull"
                >
                    <template #icon>
                        <IconSidebar />
                    </template>
                    <span class="sr-only">Toggle Sidebar Menu</span>
                </NavbarItem>

                <NavbarItem
                    :showFull="showFull"
                    href="/build/index"
                    :active="$page.url.includes('/build')"
                    class="md:mt-0 md:block"
                    :class="showFull ? 'block' : 'hidden'"
                >
                    <template #icon>
                        <IconTool
                            :stroke-width="$page.url.includes('/build') ? 2 : 1"
                        />
                    </template>
                    Build
                </NavbarItem>

                <NavbarItem
                    :showFull="showFull"
                    href="/schedule"
                    :active="$page.url.startsWith('/schedule')"
                    class="md:mt-0 md:block"
                    :class="showFull ? 'block' : 'hidden'"
                >
                    <template #icon>
                        <IconCalendarDate
                            class="stroke-icon-black"
                            :stroke-width="
                                $page.url.startsWith('/schedule') ? 2 : 1
                            "
                        />
                    </template>
                    Schedule
                </NavbarItem>

                <NavbarItem
                    :showFull="showFull"
                    href="/campaigns"
                    :active="$page.url === '/campaigns'"
                    class="md:mt-0 md:block"
                    :class="showFull ? 'block' : 'hidden'"
                >
                    <template #icon>
                        <IconBookmarkMenu
                            :stroke-width="$page.url === '/campaigns' ? 2 : 1"
                        />
                    </template>
                    Campaigns
                </NavbarItem>
            </ul>

            <ul
                class="flex w-full flex-col items-start space-y-4 border-t border-border-primary py-8 transition-all md:block"
                :class="showFull ? 'block' : 'mt-auto hidden'"
            >
                <NavbarItem
                    :showFull="showFull"
                    href="/settings/profile"
                    :active="$page.url.startsWith('/settings')"
                    class="md:block"
                    :class="showFull ? 'block' : 'hidden'"
                >
                    <template #icon>
                        <IconSettings
                            :stroke-width="
                                $page.url.startsWith('/settings') ? 2 : 1
                            "
                        />
                    </template>
                    Settings
                </NavbarItem>

                <li>
                    <span class="flex items-center gap-8 p-12">
                        <IconUser
                            class="size-32 rounded-full bg-surface-action-2x-light p-4"
                        />
                        <span v-show="showFull">
                            <div>
                                {{ user.first_name }} {{ user.last_name }}
                            </div>
                            <div class="fs-xs font-light italic">
                                {{ user.email }}
                            </div>
                        </span>
                    </span>
                </li>

                <NavbarItem :showFull="showFull" href="/logout" method="post">
                    <template #icon>
                        <IconLogout />
                    </template>
                    Logout
                </NavbarItem>
            </ul>
        </div>
    </aside>
</template>

<script setup>
import { ref, onMounted } from "vue";
import IconBell from "@/Components/Icons/IconBell.vue";
import IconHome from "@/Components/Icons/IconHome.vue";
import IconLogout from "@/Components/Icons/IconLogout.vue";
import IconSidebar from "@/Components/Icons/IconSidebar.vue";
import IconSettings from "@/Components/Icons/IconSettings.vue";
import IconUser from "@/Components/Icons/IconUser.vue";
import LogoNavbar from "@/Components/Icons/LogoNavbar.vue";
import NavbarItem from "@/Components/Dashboard/NavbarDashboard/NavbarItem.vue";
import IconLogoText from "@/Components/Icons/IconLogoText.vue";
import IconChart from "@/Components/Icons/IconChart.vue";
import IconBookmarkMenu from "@/Components/Icons/IconBookmarkMenu.vue";
import IconTool from "@/Components/Icons/IconTool.vue";
import IconCalendarDate from "@/Components/Icons/IconCalendarDate.vue";

defineProps({ user: Object });

const showFull = ref(true);

onMounted(() => {
    if (window.innerWidth < 640) {
        showFull.value = false;
    }
});
</script>
