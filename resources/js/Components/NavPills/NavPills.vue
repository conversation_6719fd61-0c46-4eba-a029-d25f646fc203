<template>
    <div class="flex rounded-full bg-surface-disabled p-8">
        <button
            v-for="(label, i) in options"
            :key="i"
            type="button"
            @click="handleClick(i)"
            class="inline-flex min-w-[110px] justify-center rounded-full px-24 py-12 transition lg:min-w-[183px]"
            :class="{
                'border border-text-action bg-surface-action-2x-light font-bold text-text-action':
                    i === selectedIndex,
            }"
        >
            {{ label }}
        </button>
    </div>
</template>

<script setup>
defineProps({
    options: Array, // e.g. ['Month', 'Week', 'List']
    selectedIndex: Number, // Currently selected button index
});

const emit = defineEmits(["update:selectedIndex"]);

const handleClick = (index) => {
    emit("update:selectedIndex", index);
};
</script>
