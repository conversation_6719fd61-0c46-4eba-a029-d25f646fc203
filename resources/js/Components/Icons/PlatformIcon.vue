<template>
    <span :class="iconClass"></span>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    platform: String
});

const iconClass = computed(() => {
    const iconMap = {
        twitter: 'i-fa-brands-x-twitter',
        facebook: 'i-fa-brands-facebook-f',
        instagram: 'i-fa-brands-instagram',
        linkedin: 'i-fa-brands-linkedin',
        pinterest: 'i-fa-brands-pinterest',
        bluesky: 'i-fa-solid-cloud',
        reddit: 'i-fa-brands-reddit-alien',
        telegram: 'i-fa-brands-telegram',
        youtube: 'i-fa-brands-youtube',
        tiktok: 'i-fa-brands-tiktok',
        gmb: 'i-fa-brands-google'
    };
    
    return iconMap[props.platform] || 'i-fa-solid-share-nodes';
});
</script> 