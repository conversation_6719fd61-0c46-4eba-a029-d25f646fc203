<template>
    <DashboardLayout title="Dashboard">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
            ref="conversationWindow"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div
                v-else
                class="relative flex min-h-full flex-col rounded-2xl bg-surface-page"
            >
                <TabsRadio
                    :tabs="tabs"
                    v-model="selectedTab"
                    @tab-click="handleTabClick"
                    :showDot="false"
                    showCloseBtn
                    class="m-16"
                    @tab-close="handleTabClose"
                    :allDisabled="findDonorsStore.isLoading"
                />

                <div
                    class="mt-auto flex flex-col justify-end space-y-24 p-16 md:space-y-40 md:p-24"
                >
                    <template
                        v-if="
                            selectedTab === 'current-chat' ||
                            selectedTab === 'campaign-chat'
                        "
                    >
                        <IconLogoGrey class="mx-auto sm:mb-32" />

                        <div
                            class="flex flex-col items-center justify-between gap-24 sm:flex-row"
                        >
                             <Module
                                @handleClick="redirectToSocial"
                            >
                                <img src="/images/social-pravi-module.png" class="w-[300px] h-[75px] object-cover"/>
                            </Module>

                            <Module
                                title="Find new donors"
                                text="Click here to start building your campaign"
                                @handleClick="startFindNewDonors"
                                :disabled="conversationSteps.length > 0"
                            >
                                <IconCalendar />
                            </Module>

                            <Module
                                title="My saved campaigns"
                                text="View your saved campaigns"
                                @handleClick="router.get('/campaigns')"
                            >
                                <IconDatabase />
                            </Module>
                            
                            <Module
                                title="Social Media Posts"
                                text="Manage your social media posts"
                                @handleClick="router.get('/social-media-posts')"
                            >
                                <IconSocialMedia />
                            </Module>
                        </div>

                        <!-- Render conversation steps -->
                        <template
                            v-for="(step, index) in conversationSteps"
                            :key="index"
                        >
                            <component
                                :is="step.component"
                                v-bind="step.props"
                                @handleNextStep="step.eventHandlers?.next"
                                @handlePreviousStep="
                                    step.eventHandlers?.previous
                                "
                                @handleNextText="step.eventHandlers?.nextText"
                            />
                        </template>

                        <LoaderBouncing
                            v-if="findDonorsStore.isLoading"
                            class="ml-28"
                            :class="showChatInput ? 'bottom-64' : 'bottom-0'"
                        />
                    </template>

                    <GPTChat
                        v-if="
                            selectedTab !== 'current-chat' &&
                            selectedTab !== 'campaign-chat'
                        "
                        :tab="getTab(selectedTab)"
                        :key="selectedTab"
                        @newMessage="newMessage"
                        :socialUrl="social_url"
                    />
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import IconLogoGrey from "@/Components/Icons/IconLogoGrey.vue";
import IconDatabase from "@/Components/Icons/IconDatabase.vue";
import IconBulb from "@/Components/Icons/IconBulb.vue";
import IconCalendar from "@/Components/Icons/IconCalendar.vue";
import IconSocialMedia from "@/Components/Icons/IconSocialMedia.vue";
import FindDonorsControls from "@/Components/FindDonorsControls/FindDonorsControls.vue";
import Module from "@/Components/Module/Module.vue";
import FindDonorsResults from "@/Components/FindDonorsResults/FindDonorsResults.vue";
import ConversationUser from "@/Components/ConversationUser/ConversationUser.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";

import ConversationPravi from "@/Components/ConversationPravi/ConversationPravi.vue";
import LoaderBouncing from "@/Components/Loaders/LoaderBouncing.vue";
import ConversionModule from "@/Components/ConversionModule/ConversionModule.vue";
import { scrollToElementEnd } from "@/utilities/helpers";
import { useFindDonorsStore } from "@/stores/findDonors";
import TabsRadio from "@/Components/Tabs/TabsRadio.vue";
import GPTChat from "../Components/GPTChat.vue";
import { router } from "@inertiajs/vue3";

const findDonorsStore = useFindDonorsStore();

const props = defineProps([
    "locations",
    "auth",
    "social_url",
    "social_media_channels",
    "communication_channels",
    "fundraising_campaigns",
]);

onMounted(() => {
    findDonorsStore.resetStore();
});

//
// Tabs handlers
// ==========================================================================

// Use the store's reactive dashboard tabs and selected tab
const tabs = computed(() => findDonorsStore.dashboardTabs);

const selectedTab = computed({
    get: () => findDonorsStore.selectedDashboardTab,
    set: (val) => findDonorsStore.setDashboardActiveTab(val),
});

function getTab(value) {
    const tab = findDonorsStore.dashboardTabs.find(
        (tab) => tab.value === value,
    );
    return tab ? tab : {};
}

const handleTabClick = async (tab) => {
    if (findDonorsStore.isLoading) return;
    findDonorsStore.setDashboardActiveTab(tab.value);
    // await scrollToElementEnd(conversationWindow.value, 500);
};
const handleTabClose = (tabValue) => {
    if (findDonorsStore.isLoading) return;
    findDonorsStore.removeDashboardTab(tabValue);
};

//
// Auto scroll handlers
// ==========================================================================

const conversationWindow = ref(null);

const newMessage = async () => {
    await scrollToElementEnd(conversationWindow.value, 500);
};

//
// Social Pravi handlers
// ==========================================================================

const redirectToSocial = () => {
    window.open(props.social_url, "_blank");
};

//
// Step progression handlers
// ==========================================================================
const conversationSteps = ref([]);

const startFindNewDonors = async () => {
    // Add conversation steps dynamically
    conversationSteps.value.push({
        component: ConversationUser,
        props: { text: "Find new donors" },
    });
    await scrollToElementEnd(conversationWindow.value, 500);

    conversationSteps.value.push({
        component: FindDonorsControls,
        props: {
            optionsLocation: props.locations,
        },
        eventHandlers: {
            next: nextDonorsStep, // Assign next step handler
            previous: previousDonorsStep, // Assign previous step handler
            nextText: nextDonorsText,
        },
    });
    scrollToElementEnd(conversationWindow.value);
};

const nextDonorsText = () => {
    conversationSteps.value.push({
        component: ConversationUser,
        props: { text: "Create donor personas" },
    });
    scrollToElementEnd(conversationWindow.value, 500);
};

const nextDonorsStep = async () => {
    if (findDonorsStore.donorsResults === null) {
        conversationSteps.value.push({
            component: ConversationPravi,
            props: {
                text: "Sorry, no results have been found. Please select a different sub category and try again.",
            },
        });
    } else {
        conversationSteps.value.push({
            component: FindDonorsResults,
            props: {
                socialMediaChannels: props.social_media_channels,
                communicationChannels: props.communication_channels,
                fundraisingCampaigns: props.fundraising_campaigns,

                hasSaveButton: true,
            },
            eventHandlers: {
                next: finalDonorsStep,
                previous: previousDonorsStep,
            },
        });
    }

    scrollToElementEnd(conversationWindow.value);
};

const finalDonorsStep = async () => {
    conversationSteps.value.splice(4);
    conversationSteps.value.push({
        component: ConversationUser,
        props: { text: "Select these donors" },
    });
    conversationSteps.value.push({
        component: ConversionModule,
    });
    await scrollToElementEnd(conversationWindow.value, 500);
};

// Handle going back to previous step
const previousDonorsStep = async () => {
    // Remove everything except the first two items from the conversationSteps array
    conversationSteps.value.splice(2);
    await scrollToElementEnd(conversationWindow.value, 500);
};
</script>
