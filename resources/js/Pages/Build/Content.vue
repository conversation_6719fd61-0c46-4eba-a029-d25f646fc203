<template>
    <DashboardLayout title="Create Campaign Content">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <!-- Enhanced Campaign Content Tabs -->
            <UnifiedContentTabs
                v-if="hasCampaignData"
                mode="campaign"
                :platformsData="props.campaignPlatformsData"
                :campaignData="campaignData"
                :audienceData="audienceData"
                :socialUrl="props.social_url"
                headerTitle="Campaign Content Creation"
                backButtonTitle="Back to campaign setup"
                scheduleButtonText="Schedule Campaign"
                scheduleButtonTitle="Schedule your campaign content"
                backRoute="/build/campaign/platforms"
                inputPlaceholder="Ask for specific campaign content adjustments..."
                @content-generated="handleContentGenerated"
            />

            <!-- Fallback for when no campaign data is available -->
            <div v-else class="flex h-[400px] items-center justify-center">
                <div class="text-center">
                    <h3 class="mb-8 text-lg font-medium text-text-body">
                        No Campaign Data Available
                    </h3>
                    <p class="text-text-secondary mb-16">
                        Please complete the campaign setup first.
                    </p>
                    <Button @click="handleBackNavigation" color="action">
                        Back to Campaign Setup
                    </Button>
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from "vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import UnifiedContentTabs from "@/Components/ContentSection/UnifiedContentTabs.vue";
import Button from "@/Components/Button/Button.vue";
import { useFindDonorsStore } from "@/stores/findDonors";
import { router } from "@inertiajs/vue3";

const props = defineProps({
    auth: Object,
    social_media_channels: Object,
    social_url: String,
    campaignPlatformsData: {
        type: Object,
        default: () => ({}),
    },
});

const campaignStore = useFindDonorsStore();

// Computed properties
const hasCampaignData = computed(() => {
    return (
        props.campaignPlatformsData &&
        Object.keys(props.campaignPlatformsData).length > 0
    );
});

const campaignData = computed(() => ({
    campaignType: campaignStore.selectedCampaignType,
    targetAmount: campaignStore.targetRaiseAmount,
    campaignDescription: campaignStore.campaignDescription,
}));

const audienceData = computed(() => {
    const firstDonor = campaignStore.donorsResults?.[0];
    return {
        gender: firstDonor?.GENDER || "",
        ageRange: firstDonor?.AGE ? [firstDonor.AGE] : [],
        salary: firstDonor?.SALARY || "",
        location: firstDonor?.LOCATION || "",
    };
});

// Methods
const handleContentGenerated = (data) => {
    // Content generated successfully - can be used for analytics or notifications
    console.log("Campaign content generated:", data);
};

const handleBackNavigation = () => {
    router.visit("/build/campaign");
};
</script>
