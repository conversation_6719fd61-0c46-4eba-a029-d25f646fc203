<template>
    <DashboardLayout title="Social Media Posts">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-16 md:px-32 md:py-24">
            <div class="relative flex min-h-full flex-col rounded-2xl bg-surface-page p-16 md:p-24">
                <!-- Success Message -->
                <div v-if="$page && $page.props && $page.props.flash && $page.props.flash.success" class="mb-24 rounded-lg bg-success-subtle border border-success p-16 flex items-start">
                    <div class="flex h-24 w-24 items-center justify-center rounded-full bg-success mr-12 flex-shrink-0">
                        <IconCheck class="h-14 w-14 text-white" />
                    </div>
                    <div>
                        <p class="font-medium">{{ $page.props.flash.success }}</p>
                        <p v-if="$page.props.flash.success && $page.props.flash.success.includes('scheduled')" class="mt-2 text-sm text-text-secondary">
                            Your post will be automatically published at the scheduled time. You can review or cancel it from the post details page.
                        </p>
                    </div>
                </div>

                <div class="mb-24 flex items-center justify-between">
                    <h1 class="fs-2xl font-bold">Social Media Posts</h1>
                    <ButtonLink :href="route('social-media-posts.create')" color="primary" class="flex items-center gap-2">
                        <IconPlus class="h-12 w-12" />
                        Create Post
                    </ButtonLink>
                </div>

                <div v-if="posts.data.length > 0" class="mb-24 grid grid-cols-1 gap-24 lg:grid-cols-2">
                    <div 
                        v-for="post in posts.data" 
                        :key="post.id" 
                        class="flex flex-col overflow-hidden rounded-lg border border-border-light bg-white shadow-sm transition hover:shadow-md"
                        :class="{'border-warning border-opacity-60': post.status === 'scheduled'}"
                    >
                        <div v-if="post.media_urls && post.media_urls.length > 0" class="relative h-200 overflow-hidden">
                            <img 
                                :src="post.media_urls[0]" 
                                class="h-full w-full object-cover"
                                alt="Post media"
                            />
                            <div v-if="post.media_urls.length > 1" class="absolute bottom-8 right-8 rounded-full bg-surface-page px-8 py-4 text-xs font-medium">
                                +{{ post.media_urls.length - 1 }} more
                            </div>
                            <div v-if="post.status === 'scheduled'" class="absolute top-12 left-12 rounded-md bg-warning px-12 py-6 text-sm font-medium text-white">
                                <div class="flex items-center gap-2">
                                    <IconCalendar class="h-12 w-12" />
                                    Scheduled
                                </div>
                            </div>
                        </div>
                        
                        <div class="flex flex-1 flex-col p-16">
                            <div class="mb-12 flex flex-wrap items-center justify-between gap-8">
                                <div class="flex flex-wrap gap-2">
                                    <Pill 
                                        v-for="platform in post.platforms" 
                                        :key="platform"
                                        class="flex items-center gap-1"
                                    >
                                        <PlatformIcon :platform="platform" class="h-14 w-14" />
                                        {{ getPlatformLabel(platform) }}
                                    </Pill>
                                </div>
                                <div class="text-xs">
                                    <div v-if="post.status === 'scheduled'" class="flex items-center gap-1 text-warning">
                                        <IconCalendar class="h-12 w-12" />
                                        <span class="font-medium">Scheduled for:</span>
                                    </div>
                                    <div v-else class="text-text-secondary">
                                        Posted on:
                                    </div>
                                    <div class="font-medium mt-1" :class="{'text-warning': post.status === 'scheduled'}">
                                        {{ formatDate(post.scheduled_at || post.created_at) }}
                                    </div>
                                </div>
                            </div>
                            
                            <p class="mb-16 flex-1">{{ post.content }}</p>
                            
                            <div class="flex justify-end gap-8">
                                <ButtonLink 
                                    :href="route('social-media-posts.show', post.id)" 
                                    color="secondary"
                                    class="flex items-center gap-2 text-sm"
                                >
                                    <IconEye class="h-14 w-14" />
                                    View Details
                                </ButtonLink>
                                <Button 
                                    color="danger" 
                                    @click="confirmDelete(post)"
                                    class="flex items-center gap-2 text-sm"
                                >
                                    <IconTrash class="h-14 w-14" />
                                    Delete
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div v-else class="flex flex-col items-center justify-center rounded-lg border border-dashed border-border-primary bg-surface-highlight p-48">
                    <IconSocialMedia class="mb-16 h-64 w-64 text-text-secondary opacity-60" />
                    <h3 class="mb-8 text-lg font-medium">No posts found</h3>
                    <p class="mb-16 text-center text-text-secondary">Share content across your social platforms with just a few clicks</p>
                    <ButtonLink :href="route('social-media-posts.create')" color="primary" class="flex items-center gap-2">
                        <IconPlus class="h-18 w-18" />
                        Create Your First Post
                    </ButtonLink>
                </div>

                <Pagination v-if="posts.data.length > 0" class="mt-24" :posts="posts" @changePage="changePage" />
            </div>
        </main>

        <!-- Delete Modal -->
        <Modal :show="confirmingDelete" @close="closeModal">
            <div class="p-24">
                <div class="mb-16 flex items-center gap-16">
                    <div class="flex h-40 w-40 items-center justify-center rounded-full bg-danger-subtle">
                        <IconTrash class="h-20 w-20 text-danger" />
                    </div>
                    <h2 class="fs-xl font-bold">Delete Post</h2>
                </div>
                
                <p class="mb-8">Are you sure you want to delete this post? This action cannot be undone and will remove the post from your dashboard.</p>
                
                <div v-if="postToDelete && hasPlatformWithLimitations(postToDelete)" class="mb-16 rounded-lg bg-warning-subtle p-12 text-sm">
                    <p class="font-medium">Platform Limitations:</p>
                    <p>Instagram and TikTok do not support deletion via API. For these platforms, you'll need to manually delete the post using their mobile apps.</p>
                    <p v-if="hasStoryContent(postToDelete)">Facebook image stories cannot be deleted via API and must be deleted manually from the Facebook app.</p>
                </div>
                
                <div class="flex justify-end gap-8">
                    <Button color="secondary" @click="closeModal">Cancel</Button>
                    <Button color="danger" @click="deletePost" class="flex items-center gap-2">
                        <IconTrash class="h-12 w-12" />
                        Delete Post
                    </Button>
                </div>
            </div>
        </Modal>
    </DashboardLayout>
</template>

<script setup>
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Button from '@/Components/Button/Button.vue';
import ButtonLink from '@/Components/ButtonLink/ButtonLink.vue';
import Pill from '@/Components/Pill/Pill.vue';
import Modal from '@/Components/Modal.vue';
import Pagination from '@/Components/Pagination/Pagination.vue';
import IconSocialMedia from '@/Components/Icons/IconSocialMedia.vue';
import IconPlus from '@/Components/Icons/IconPlus.vue';
import IconTrash from '@/Components/Icons/IconTrash.vue';
import IconEye from '@/Components/Icons/IconEye.vue';
import PlatformIcon from '@/Components/Icons/PlatformIcon.vue';
import IconCalendar from '@/Components/Icons/IconCalendar.vue';
import IconCheck from '@/Components/Icons/IconCheck.vue';

const props = defineProps({
    posts: Object,
    platforms: Array,
});

const confirmingDelete = ref(false);
const postToDelete = ref(null);

function confirmDelete(post) {
    postToDelete.value = post;
    confirmingDelete.value = true;
}

function closeModal() {
    confirmingDelete.value = false;
    postToDelete.value = null;
}

function deletePost() {
    if (!postToDelete.value || !postToDelete.value.id) {
        console.error('No post selected for deletion');
        closeModal();
        return;
    }
    
    router.delete(route('social-media-posts.destroy', postToDelete.value.id), {
        onSuccess: () => {
            closeModal();
        },
        onError: (errors) => {
            console.error('Error deleting post:', errors);
            // Still close the modal but let the user know there was an error
            closeModal();
            alert('There was an error deleting the post. Please try again.');
        },
        preserveScroll: true
    });
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
}

function getPlatformLabel(platformValue) {
    const platform = props.platforms.find(p => p.value === platformValue);
    return platform ? platform.label : platformValue;
}

function changePage(page) {
    router.get(route('social-media-posts.index', { page }), {}, { preserveState: true });
}

function hasPlatformWithLimitations(post) {
    return post.platforms.includes('instagram') || post.platforms.includes('tiktok');
}

function hasStoryContent(post) {
    return post.content.includes('story');
}
</script> 