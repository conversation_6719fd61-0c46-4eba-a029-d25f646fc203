<template>
    <DashboardLayout title="Social Media Post Details">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-16 md:px-32 md:py-24">
            <div class="relative flex min-h-full flex-col rounded-2xl bg-surface-page p-16 md:p-24">
                <!-- Header with breadcrumb -->
                <div class="mb-24 flex flex-col gap-8">
                    <div class="flex items-center gap-2 text-sm text-text-secondary">
                        <ButtonLink 
                            :href="route('social-media-posts.index')" 
                            class="flex items-center gap-2 text-text-secondary hover:text-primary"
                            color="transparent"
                        >
                            <IconArrowLeft class="h-12 w-12" />
                            Back to Posts
                        </ButtonLink>
                        <span class="mx-2">/</span>
                        <span>Post Details</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <h1 class="fs-2xl font-bold">Post Details</h1>
                        <Button 
                            color="danger" 
                            @click="confirmDelete"
                            class="flex items-center gap-2"
                        >
                            <IconTrash class="h-12 w-12" />
                            Delete Post
                        </Button>
                    </div>
                </div>

                <div class="grid grid-cols-1 gap-24 lg:grid-cols-3">
                    <!-- Post details column -->
                    <div class="lg:col-span-2 lg:order-2">
                        <!-- Post card -->
                        <div class="mb-24 overflow-hidden rounded-lg border border-border-light bg-white shadow-sm">
                            <div class="border-b border-border-light p-16">
                                <div class="mb-12 flex items-center gap-8">
                                    <div class="h-36 w-36 overflow-hidden rounded-full bg-primary-light">
                                        <IconUser class="h-full w-full p-6 text-primary" />
                                    </div>
                                    <div>
                                        <h3 class="font-medium">Your Post</h3>
                                        <p class="text-xs text-text-secondary">
                                            {{ post.status === 'scheduled' ? 'Scheduled for' : 'Posted on' }} {{ formatDate(post.scheduled_at || post.created_at) }}
                                        </p>
                                    </div>
                                </div>
                                <div v-if="post.status === 'scheduled'" class="flex items-center gap-2 rounded-lg bg-warning-subtle px-12 py-8">
                                    <IconCalendar class="h-16 w-16 text-warning" />
                                    <span class="text-sm font-medium">This post is scheduled and will be published automatically at the scheduled time.</span>
                                </div>
                            </div>
                            
                            <!-- Post content -->
                            <div class="p-16">
                                <p class="mb-16 whitespace-pre-wrap text-lg">{{ post.content }}</p>
                                
                                <!-- Media gallery -->
                                <div v-if="post.media_urls && post.media_urls.length > 0" class="mt-16 overflow-hidden rounded-lg bg-surface-grey-3x-light">
                                    <!-- Single image layout -->
                                    <div v-if="post.media_urls.length === 1" class="flex justify-center">
                                        <img 
                                            :src="post.media_urls[0]" 
                                            class="max-h-320 max-w-full object-contain"
                                            alt="Media"
                                        />
                                    </div>
                                    
                                    <!-- Two images layout -->
                                    <div v-else-if="post.media_urls.length === 2" class="grid grid-cols-2">
                                        <div v-for="(media, index) in post.media_urls" :key="index" class="h-180">
                                            <img 
                                                :src="media" 
                                                class="h-full w-full object-cover"
                                                alt="Media"
                                            />
                                        </div>
                                    </div>
                                    
                                    <!-- Three images layout -->
                                    <div v-else-if="post.media_urls.length === 3" class="grid grid-cols-2 grid-rows-2">
                                        <div class="row-span-2">
                                            <img 
                                                :src="post.media_urls[0]" 
                                                class="h-full w-full object-cover"
                                                alt="Media"
                                            />
                                        </div>
                                        <div v-for="(media, index) in post.media_urls.slice(1, 3)" :key="index" class="h-140">
                                            <img 
                                                :src="media" 
                                                class="h-full w-full object-cover"
                                                alt="Media"
                                            />
                                        </div>
                                    </div>
                                    
                                    <!-- Four or more images layout -->
                                    <div v-else class="grid grid-cols-2">
                                        <div v-for="(media, index) in post.media_urls.slice(0, 4)" :key="index" class="h-140">
                                            <img 
                                                :src="media" 
                                                class="h-full w-full object-cover"
                                                alt="Media"
                                            />
                                        </div>
                                        <div v-if="post.media_urls.length > 4" class="relative col-start-2 row-start-2">
                                            <img 
                                                :src="post.media_urls[4]" 
                                                class="h-full w-full object-cover brightness-50"
                                                alt="Media"
                                            />
                                            <div class="absolute inset-0 flex items-center justify-center">
                                                <span class="text-xl font-bold text-white">+{{ post.media_urls.length - 4 }} more</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Platforms pill list -->
                                <div class="mt-16 flex flex-wrap gap-2">
                                    <Pill 
                                        v-for="platform in post.platforms" 
                                        :key="platform"
                                        class="flex items-center gap-1"
                                    >
                                        <PlatformIcon :platform="platform" class="h-12 w-12" />
                                        {{ platformLabels[platform] || platform }}
                                    </Pill>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Platform status cards -->
                        <div v-if="post.post_data && post.post_data.postIds">
                            <h2 class="mb-16 text-lg font-bold">Publishing Results</h2>
                            <div class="grid grid-cols-1 gap-16 sm:grid-cols-2">
                                <div 
                                    v-for="platformData in post.post_data.postIds" 
                                    :key="platformData.platform" 
                                    class="flex flex-col rounded-lg border border-border-light bg-white p-16 shadow-sm"
                                >
                                    <!-- Platform header -->
                                    <div class="mb-12 flex items-center gap-8">
                                        <div class="flex h-32 w-32 items-center justify-center rounded-md bg-surface-grey-light">
                                            <PlatformIcon :platform="platformData.platform" class="h-18 w-18" />
                                        </div>
                                        <div>
                                            <h3 class="font-medium capitalize">{{ platformLabels[platformData.platform] || platformData.platform }}</h3>
                                            <div class="flex items-center gap-2">
                                                <span 
                                                    class="inline-block h-8 w-8 rounded-full"
                                                    :class="platformData.status === 'success' ? 'bg-success' : 'bg-danger'"
                                                ></span>
                                                <span class="text-xs capitalize">{{ platformData.status }}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Platform details -->
                                    <div class="space-y-8 rounded-md bg-surface-grey-2x-light p-12 text-sm">
                                        <div v-if="platformData.id" class="flex items-center justify-between gap-2">
                                            <span class="font-medium">Post ID:</span>
                                            <code class="rounded bg-surface-grey-light px-4 py-2 text-xs">{{ truncateId(platformData.id) }}</code>
                                        </div>
                                        
                                        <div v-if="platformData.postUrl" class="flex">
                                            <a 
                                                :href="platformData.postUrl" 
                                                target="_blank" 
                                                class="flex w-full items-center justify-center gap-2 rounded-md bg-primary/10 px-8 py-4 text-primary transition hover:bg-primary/20"
                                            >
                                                <IconExternalLink class="h-12 w-12" />
                                                View on {{ platformLabels[platformData.platform] || platformData.platform }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sidebar with post metadata -->
                    <div class="lg:col-span-1 lg:order-1">
                        <div class="sticky top-24 space-y-16">
                            <!-- Post info card -->
                            <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                                <h3 class="mb-12 font-medium">Post Information</h3>
                                <div class="space-y-8">
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">Status:</span>
                                        <span class="font-medium capitalize">{{ post.status }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">Created:</span>
                                        <span class="font-medium">{{ formatDate(post.created_at) }}</span>
                                    </div>
                                    <div v-if="post.status === 'scheduled'" class="flex justify-between">
                                        <span class="text-text-secondary">Scheduled For:</span>
                                        <span class="font-medium">{{ formatDate(post.scheduled_at) }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">Platforms:</span>
                                        <span class="font-medium">{{ post.platforms.length }}</span>
                                    </div>
                                    <div class="flex justify-between">
                                        <span class="text-text-secondary">Media:</span>
                                        <span class="font-medium">{{ post.media_urls?.length || 0 }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Post actions card -->
                            <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                                <h3 class="mb-12 font-medium">Actions</h3>
                                <div class="space-y-8">
                                    <ButtonLink 
                                        :href="route('social-media-posts.index')" 
                                        color="secondary" 
                                        class="flex w-full items-center justify-center gap-2"
                                    >
                                        <IconArrowLeft class="h-12 w-12" />
                                        Back to Posts
                                    </ButtonLink>
                                    <Button 
                                        color="danger" 
                                        @click="confirmDelete"
                                        class="flex w-full items-center justify-center gap-2"
                                    >
                                        <IconTrash class="h-12 w-12" />
                                        Delete Post
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Delete confirmation modal -->
        <Modal :show="confirmingDelete" @close="closeModal">
            <div class="p-24">
                <div class="mb-16 flex items-center gap-16">
                    <div class="flex h-40 w-40 items-center justify-center rounded-full bg-danger-subtle">
                        <IconTrash class="h-20 w-20 text-danger" />
                    </div>
                    <h2 class="fs-xl font-bold">Delete Post</h2>
                </div>
                
                <p class="mb-8">Are you sure you want to delete this post? This action cannot be undone and will remove the post from your dashboard.</p>
                
                <div v-if="hasPlatformWithLimitations(post)" class="mb-16 rounded-lg bg-warning-subtle p-12 text-sm">
                    <p class="font-medium">Platform Limitations:</p>
                    <p>Instagram and TikTok do not support deletion via API. For these platforms, you'll need to manually delete the post using their mobile apps.</p>
                    <p v-if="hasStoryContent(post)">Facebook image stories cannot be deleted via API and must be deleted manually from the Facebook app.</p>
                </div>
                
                <div class="flex justify-end gap-8">
                    <Button color="secondary" @click="closeModal">Cancel</Button>
                    <Button color="danger" @click="deletePost" class="flex items-center gap-2">
                        <IconTrash class="h-12 w-12" />
                        Delete Post
                    </Button>
                </div>
            </div>
        </Modal>
    </DashboardLayout>
</template>

<script setup>
import { ref } from 'vue';
import { router } from '@inertiajs/vue3';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Button from '@/Components/Button/Button.vue';
import ButtonLink from '@/Components/ButtonLink/ButtonLink.vue';
import Pill from '@/Components/Pill/Pill.vue';
import Modal from '@/Components/Modal.vue';
import IconTrash from '@/Components/Icons/IconTrash.vue';
import IconArrowLeft from '@/Components/Icons/IconArrowLeft.vue';
import IconUser from '@/Components/Icons/IconUser.vue';
import IconExternalLink from '@/Components/Icons/IconExternalLink.vue';
import PlatformIcon from '@/Components/Icons/PlatformIcon.vue';
import IconCalendar from '@/Components/Icons/IconCalendar.vue';

const props = defineProps({
    post: Object,
    platformLabels: Object,
});

const confirmingDelete = ref(false);

function confirmDelete() {
    confirmingDelete.value = true;
}

function closeModal() {
    confirmingDelete.value = false;
}

function deletePost() {
    if (!props.post || !props.post.id) {
        console.error('No post available for deletion');
        closeModal();
        return;
    }
    
    router.delete(route('social-media-posts.destroy', props.post.id), {
        onSuccess: () => {
            router.visit(route('social-media-posts.index'));
        },
        onError: (errors) => {
            console.error('Error deleting post:', errors);
            closeModal();
            alert('There was an error deleting the post. Please try again.');
        },
        preserveScroll: true
    });
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    });
}

function truncateId(id) {
    if (typeof id !== 'string' || id.length < 30) return id;
    return id.substring(0, 15) + '...' + id.substring(id.length - 5);
}

function hasPlatformWithLimitations(post) {
    return post.platforms.includes('instagram') || post.platforms.includes('tiktok');
}

function hasStoryContent(post) {
    return post.content.includes('story');
}
</script> 