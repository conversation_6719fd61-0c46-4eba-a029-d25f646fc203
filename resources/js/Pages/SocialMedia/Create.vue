<template>
    <DashboardLayout title="Create Social Media Post">
        <main class="w-full overflow-auto bg-surface-grey-2x-light p-16 md:px-32 md:py-24">
            <div class="relative flex min-h-full flex-col rounded-2xl bg-surface-page p-16 md:p-24">
                <!-- Header -->
                <div class="mb-24 flex items-center justify-between">
                    <div class="flex items-center gap-12">
                        <IconSocialMedia class="h-32 w-32 text-primary" />
                        <h1 class="fs-2xl font-bold">Create Social Media Post</h1>
                    </div>
                    <ButtonLink :href="route('social-media-posts.index')" color="secondary" class="flex items-center gap-2">
                        <IconArrowLeft class="h-16 w-16" />
                        Back to Posts
                    </ButtonLink>
                </div>

                <form @submit.prevent="submitForm" class="space-y-24">
                    <!-- Post preview card -->
                    <div class="mx-auto mb-24 w-full max-w-lg overflow-hidden rounded-lg border border-border-light bg-white shadow-sm md:mb-32">
                        <div class="p-16">
                            <div class="mb-12 flex items-center gap-8">
                                <div class="h-40 w-40 overflow-hidden rounded-full bg-primary-light">
                                    <IconUser class="h-full w-full p-8 text-primary" />
                                </div>
                                <div>
                                    <p class="font-medium">Your Post</p>
                                    <p class="text-xs text-text-secondary">{{ getSelectedPlatformsText() }}</p>
                                </div>
                            </div>
                            
                            <div class="min-h-64 rounded-md bg-surface-grey-light p-12">
                                <p v-if="form.content" class="whitespace-pre-wrap">{{ form.content }}</p>
                                <p v-else class="italic text-text-secondary">What's on your mind?</p>
                            </div>
                            
                            <div v-if="hasMediaUrls" class="mt-8 flex w-full items-center space-x-2 overflow-x-auto">
                                <div 
                                    v-for="(url, index) in validMediaUrls" 
                                    :key="index" 
                                    class="relative h-96 w-96 flex-shrink-0 overflow-hidden rounded-md border border-border-light"
                                >
                                    <img 
                                        :src="url" 
                                        class="h-full w-full object-cover" 
                                        alt="Media preview"
                                        @error="handleImageError"
                                    />
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content section -->
                    <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                        <h2 class="mb-12 text-lg font-medium">Post Content</h2>
                        <TextArea
                            id="content"
                            v-model="form.content"
                            class="mt-4 w-full"
                            :error="form.errors.content"
                            placeholder="What's on your mind?"
                            rows="4"
                        />
                        <InputError :message="form.errors.content" />
                        <p class="mt-2 text-xs text-text-secondary">
                            {{ form.content.length }} / 280 characters
                        </p>
                    </div>

                    <!-- Platforms section -->
                    <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                        <h2 class="mb-12 text-lg font-medium">Select Platforms</h2>
                        <p class="mb-12 text-sm text-text-secondary">
                            Choose where you want to publish this post
                        </p>
                        
                        <div class="grid grid-cols-1 gap-12 sm:grid-cols-2 md:grid-cols-3">
                            <div 
                                v-for="platform in platforms" 
                                :key="platform.value" 
                                class="flex cursor-pointer items-center gap-8 rounded-md border p-12 transition hover:border-primary hover:bg-primary-light/10"
                                :class="form.platforms.includes(platform.value) ? 'border-primary bg-primary-light/10' : 'border-border-light'"
                                @click="togglePlatform(platform.value)"
                            >
                                <div class="flex h-36 w-36 items-center justify-center rounded-md bg-surface-grey-light">
                                    <PlatformIcon :platform="platform.value" class="h-20 w-20" />
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium">{{ platform.label }}</p>
                                </div>
                                <Checkbox 
                                    :id="platform.value"
                                    v-model:checked="form.platforms"
                                    :value="platform.value"
                                    class="pointer-events-none"
                                />
                            </div>
                        </div>
                        <InputError :message="form.errors.platforms" />
                    </div>

                    <!-- Media section -->
                    <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                        <h2 class="mb-12 text-lg font-medium">Media</h2>
                        <p class="mb-12 text-sm text-text-secondary">
                            Add images to your post (optional)
                        </p>
                        
                        <div class="space-y-8">
                            <div 
                                v-for="(url, index) in form.media_urls" 
                                :key="index" 
                                class="flex items-center gap-8"
                            >
                                <div class="flex h-36 w-36 items-center justify-center rounded-md bg-surface-grey-light">
                                    <IconImage class="h-20 w-20 text-text-secondary" />
                                </div>
                                <TextInput 
                                    v-model="form.media_urls[index]" 
                                    class="flex-1" 
                                    placeholder="https://example.com/image.jpg"
                                />
                                <Button 
                                    type="button" 
                                    color="danger" 
                                    @click="removeMediaUrl(index)"
                                    class="flex h-36 w-36 items-center justify-center"
                                >
                                    <IconTrash class="h-20 w-20" />
                                </Button>
                            </div>
                            
                            <Button 
                                type="button" 
                                @click="addMediaUrl" 
                                class="mt-8 flex w-full items-center justify-center gap-8 border border-dashed border-border-primary py-12"
                                color="secondary"
                            >
                                <IconPlus class="h-20 w-20" />
                                Add Media URL
                            </Button>
                        </div>
                        <InputError :message="form.errors.media_urls" />
                    </div>

                    <!-- Schedule section -->
                    <div class="rounded-lg border border-border-light bg-white p-16 shadow-sm">
                        <h2 class="mb-12 text-lg font-medium">Publishing Options</h2>
                        
                        <!-- Publishing option toggle buttons -->
                        <div class="grid grid-cols-2 gap-4 mb-16">
                            <button 
                                type="button"
                                @click="form.scheduled_at = null"
                                class="flex flex-col items-center justify-center p-16 rounded-lg border-2 transition-all"
                                :class="!form.scheduled_at 
                                    ? 'border-primary bg-primary-light/10 shadow-sm' 
                                    : 'border-border-light hover:border-primary hover:bg-primary-light/5'"
                            >
                                <div class="flex h-48 w-48 items-center justify-center rounded-full bg-primary-light mb-8">
                                    <IconSend class="h-24 w-24 text-primary" />
                                </div>
                                <span class="font-medium">Publish Now</span>
                                <span class="text-xs text-text-secondary mt-2">Post immediately to all platforms</span>
                            </button>
                            
                            <button 
                                type="button"
                                @click="initializeScheduling"
                                class="flex flex-col items-center justify-center p-16 rounded-lg border-2 transition-all"
                                :class="form.scheduled_at 
                                    ? 'border-primary bg-primary-light/10 shadow-sm' 
                                    : 'border-border-light hover:border-primary hover:bg-primary-light/5'"
                            >
                                <div class="flex h-48 w-48 items-center justify-center rounded-full bg-primary-light mb-8">
                                    <IconCalendar class="h-24 w-24 text-primary" />
                                </div>
                                <span class="font-medium">Schedule for Later</span>
                                <span class="text-xs text-text-secondary mt-2">Set a specific date and time</span>
                            </button>
                        </div>
                        
                        <!-- Scheduling fields (visible only when Schedule is selected) -->
                        <div v-if="form.scheduled_at" class="mt-24 border-t border-border-light pt-16">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-16">
                                <div>
                                    <InputLabel for="scheduled_date" value="Date" class="mb-2" />
                                    <div class="flex items-center">
                                        <div class="flex h-40 w-40 items-center justify-center rounded-l-md bg-surface-grey-light">
                                            <IconCalendar class="h-18 w-18 text-text-secondary" />
                                        </div>
                                        <TextInput
                                            id="scheduled_date"
                                            type="date"
                                            class="rounded-l-none w-full"
                                            v-model="scheduledDate"
                                            :min="minDate"
                                            required
                                        />
                                    </div>
                                    <InputError :message="form.errors.scheduled_at" class="mt-2" />
                                </div>
                                
                                <div>
                                    <InputLabel for="scheduled_time" value="Time" class="mb-2" />
                                    <div class="flex items-center">
                                        <div class="flex h-40 w-40 items-center justify-center rounded-l-md bg-surface-grey-light">
                                            <IconClock class="h-18 w-18 text-text-secondary" />
                                        </div>
                                        <TextInput
                                            id="scheduled_time"
                                            type="time"
                                            class="rounded-l-none w-full"
                                            v-model="scheduledTime"
                                            required
                                        />
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-16 rounded-md bg-surface-grey-light p-16 text-sm">
                                <div class="flex items-center text-text-primary mb-4">
                                    <IconCalendar class="h-16 w-16 mr-8" />
                                    <span class="font-medium">Scheduled Post Summary</span>
                                </div>
                                <div class="ml-24">
                                    <p class="mb-2">Your post will be published on:</p>
                                    <p class="font-medium text-base">{{ formatScheduledDateTime() }}</p>
                                    <p class="mt-8 text-xs text-text-secondary">
                                        The post will be automatically published to all selected platforms at the scheduled time.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="sticky bottom-0 flex justify-between gap-12 rounded-md bg-white px-16 py-12 shadow-md">
                        <ButtonLink :href="route('social-media-posts.index')" color="secondary">
                            Cancel
                        </ButtonLink>
                        <Button 
                            type="submit" 
                            color="primary" 
                            :disabled="form.processing || !form.content || form.platforms.length === 0"
                            class="flex items-center gap-8 px-24"
                        >
                            <template v-if="form.scheduled_at">
                                <IconCalendar class="h-16 w-16" />
                                Schedule Post
                            </template>
                            <template v-else>
                                <IconSend class="h-16 w-16" />
                                Publish Now
                            </template>
                        </Button>
                    </div>
                </form>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { router, useForm } from '@inertiajs/vue3';
import DashboardLayout from '@/Layouts/DashboardLayout.vue';
import Button from '@/Components/Button/Button.vue';
import ButtonLink from '@/Components/ButtonLink/ButtonLink.vue';
import TextArea from '@/Components/TextArea/TextArea.vue';
import TextInput from '@/Components/TextInput/TextInput.vue';
import Checkbox from '@/Components/Checkbox/Checkbox.vue';
import InputLabel from '@/Components/InputLabel.vue';
import InputError from '@/Components/InputError.vue';
import IconPlus from '@/Components/Icons/IconPlus.vue';
import IconTrash from '@/Components/Icons/IconTrash.vue';
import IconSocialMedia from '@/Components/Icons/IconSocialMedia.vue';
import IconArrowLeft from '@/Components/Icons/IconArrowLeft.vue';
import IconSend from '@/Components/Icons/IconSend.vue';
import IconImage from '@/Components/Icons/IconImage.vue';
import IconUser from '@/Components/Icons/IconUser.vue';
import PlatformIcon from '@/Components/Icons/PlatformIcon.vue';
import IconCalendar from '@/Components/Icons/IconCalendar.vue';
import IconClock from '@/Components/Icons/IconClock.vue';

const props = defineProps({
    platforms: {
        type: Array,
        required: true
    }
});

const form = useForm({
    content: '',
    platforms: [],
    media_urls: [],
    scheduled_at: null,
});

const validMediaUrls = computed(() => {
    return form.media_urls.filter(url => {
        try {
            new URL(url);
            return url.trim() !== '';
        } catch (e) {
            return false;
        }
    });
});

const hasMediaUrls = computed(() => {
    return validMediaUrls.value.length > 0;
});

// Scheduling related properties
const scheduledDate = ref('');
const scheduledTime = ref('');

const minDate = computed(() => {
    const today = new Date();
    return today.toISOString().split('T')[0];
});

function initializeScheduling() {
    if (!form.scheduled_at) {
        // Set default values to tomorrow at current time
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        
        scheduledDate.value = tomorrow.toISOString().split('T')[0];
        
        const hours = String(tomorrow.getHours()).padStart(2, '0');
        const minutes = String(tomorrow.getMinutes()).padStart(2, '0');
        scheduledTime.value = `${hours}:${minutes}`;
        
        updateScheduledAt();
    }
}

function updateScheduledAt() {
    if (scheduledDate.value && scheduledTime.value) {
        const dateTime = new Date(`${scheduledDate.value}T${scheduledTime.value}`);
        form.scheduled_at = dateTime.toISOString();
    }
}

function formatScheduledDateTime() {
    if (form.scheduled_at) {
        const date = new Date(form.scheduled_at);
        return date.toLocaleString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
        });
    }
    return 'Not scheduled';
}

// Watch for changes to update the scheduled_at value
watch([scheduledDate, scheduledTime], () => {
    updateScheduledAt();
});

function togglePlatform(platformValue) {
    const index = form.platforms.indexOf(platformValue);
    if (index === -1) {
        form.platforms.push(platformValue);
    } else {
        form.platforms.splice(index, 1);
    }
}

function getSelectedPlatformsText() {
    if (form.platforms.length === 0) {
        return 'No platforms selected';
    }
    
    const platformLabels = form.platforms.map(value => {
        const platform = props.platforms.find(p => p.value === value);
        return platform ? platform.label : value;
    });
    
    if (platformLabels.length === 1) {
        return `Posting to ${platformLabels[0]}`;
    } else if (platformLabels.length === 2) {
        return `Posting to ${platformLabels[0]} and ${platformLabels[1]}`;
    } else {
        return `Posting to ${platformLabels.length} platforms`;
    }
}

function handleImageError(event) {
    event.target.src = '/img/placeholder-image.jpg';
}

function addMediaUrl() {
    form.media_urls.push('');
}

function removeMediaUrl(index) {
    form.media_urls.splice(index, 1);
}

function submitForm() {
    // Filter out empty media_urls
    form.media_urls = form.media_urls.filter(url => url.trim() !== '');
    
    // Validate URLs are properly formatted
    const validUrls = form.media_urls.every(url => {
        try {
            new URL(url);
            return true;
        } catch (e) {
            return false;
        }
    });
    
    if (form.media_urls.length > 0 && !validUrls) {
        alert('Please enter valid media URLs');
        return;
    }
    
    form.post(route('social-media-posts.store'), {
        onSuccess: () => {
            router.visit(route('social-media-posts.index'));
        }
    });
}
</script> 