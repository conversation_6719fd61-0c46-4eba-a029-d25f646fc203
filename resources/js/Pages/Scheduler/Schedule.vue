<template>
    <DashboardLayout title="Schedule your content">
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div
                v-else
                class="relative min-h-full flex-col rounded-2xl bg-surface-page"
            >
                <div
                    class="mx-16 border-b border-border-primary py-48 text-center lg:mx-32"
                >
                    <Button
                        color="action"
                        :icon-left="IconSend"
                        @click="handlePostNow"
                        class="h-56 rounded-2xl"
                    >
                        Post now
                    </Button>
                </div>

                <div
                    class="mx-auto flex max-w-screen-lg flex-col items-center px-16 py-48"
                >
                    <DatePickerSwiper v-model="selectedDate" />

                    <VueDatePicker
                        v-model="selectedTime"
                        time-picker
                        inline
                        auto-apply
                        is-24
                        format="HH:mm"
                        :action-row="{
                            showNow: false,
                            showPreview: false,
                            showSelect: false,
                        }"
                        class="max-w-[260px]"
                    />

                    <div class="-mt-4">
                        <label for="frequency" class="mb-8 block">
                            How often would you like to post?
                        </label>
                        <Radio
                            id="frequency"
                            v-model="selectedFrequency"
                            :options="frequencyOptions"
                            :columnLayout="true"
                        />
                    </div>

                    <Button
                        color="action"
                        @click="handleSchedulePost"
                        :disabled="!isScheduleReady"
                        :icon-left="IconCalendarDate"
                        class="group h-56 self-end rounded-2xl lg:-mt-32"
                    >
                        Schedule post
                    </Button>
                </div>
            </div>
        </main>

        <Toast
            v-if="toast.message"
            :text="toast.message"
            :type="toast.type"
            @handleClose="toast.message = ''"
        ></Toast>
    </DashboardLayout>
</template>

<script setup>
import { ref, computed } from "vue";
import { parseISO, startOfDay, isBefore, format } from "date-fns";
import { useFindDonorsStore } from "@/stores/findDonors";

import Radio from "@/Components/Radio/Radio.vue";
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import Button from "@/Components/Button/Button.vue";
import NoSubCard from "@/Components/NoSubCard/NoSubCard.vue";
import IconSend from "@/Components/Icons/IconSend.vue";
import IconCalendarDate from "@/Components/Icons/IconCalendarDate.vue";
import DatePickerSwiper from "@/Components/Calendars/DatePickerSwiper.vue";
import Toast from "@/Components/Toast/Toast.vue";

import VueDatePicker from "@vuepic/vue-datepicker";
import "@vuepic/vue-datepicker/dist/main.css";

const props = defineProps({
    auth: Object,
});

// TODO: retrieve the posts to be scheduled from the API or store

const frequencyOptions = [
    {
        name: "Assistant suggestion",
        value: "assistant_suggestion",
        id: 1,
    },
    {
        name: "Daily",
        value: "daily",
        id: 2,
    },
    {
        name: "Every other day",
        value: "every_other_day",
        id: 3,
    },
    {
        name: "Weekly",
        value: "weekly",
        id: 4,
    },
    {
        name: "Bi-Weekly",
        value: "bi_weekly",
        id: 5,
    },
];

// --- Helper function ---
function getValidStartDate(candidateDate) {
    const today = startOfDay(new Date());

    if (candidateDate) {
        const candidate = startOfDay(parseISO(candidateDate));
        if (!isBefore(candidate, today)) {
            return format(candidate, "yyyy-MM-dd");
        }
    }

    return format(today, "yyyy-MM-dd");
}

// --- Store and date setup ---
const { campaignStartDate } = useFindDonorsStore(); // 'YYYY-MM-DD' or null
const selectedDate = ref(getValidStartDate(campaignStartDate));

// --- Time setup ---
const now = new Date();
const selectedTime = ref({
    hours: format(now, "HH"),
    minutes: format(now, "mm"),
});

// --- Frequency setup ---
const selectedFrequency = ref(null);

const isScheduleReady = computed(() => {
    return selectedDate.value && selectedTime.value && selectedFrequency.value;
});

// --- Post buttons actions ---

const toast = ref({
    message: "",
    type: "",
});

const handlePostNow = () => {
    // placeholder - for if a datetime is required for the API
    const now = new Date();
    const isoString = now.toISOString(); // ISO 8601

    // TODO: API integration for posting now
    // For now, just log the current date and time
    console.log("Post Now - Current DateTime:", isoString);

    // Show success message
    toast.value = {
        message: "Post created successfully!",
        type: "success",
    };
};

const handleSchedulePost = () => {
    if (!isScheduleReady.value) {
        toast.value = {
            message: "Please select a date, time, and frequency.",
            type: "danger",
        };
        console.warn("Cannot schedule post: missing fields.");
        return;
    }

    const { hours, minutes } = selectedTime.value;
    const date = new Date(selectedDate.value); // selectedDate is "YYYY-MM-DD"
    date.setHours(hours);
    date.setMinutes(minutes);
    date.setSeconds(0);

    // Checks if scheduled datetime is in the past
    const now = new Date();
    if (date < now) {
        toast.value = {
            message:
                "You can't schedule a post in the past. Please choose a future time.",
            type: "danger",
        };
        return;
    }

    const isoString = date.toISOString();

    // TODO: API integration for scheduling a post
    // For now, just log the selected date, time, and frequency
    console.log("Schedule Post - Frequency:", selectedFrequency.value);
    console.log("Schedule Post - DateTime (ISO):", isoString);

    // Proceed with API call or store action here

    // Show success message
    toast.value = {
        message: "Post scheduled successfully!",
        type: "success",
    };
};
</script>

<style>
.dp__time_col_reg_block {
    padding: 0 8px;
}

.dp__time_display {
    font-family: "Manrope", "sans-serif";
}
</style>
