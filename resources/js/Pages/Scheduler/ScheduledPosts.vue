<template>
    <DashboardLayout>
        <main
            class="w-full overflow-auto bg-surface-grey-2x-light p-0 pt-80 sm:p-16 md:px-32 md:py-24"
        >
            <NoSubCard v-if="!auth.user.has_stripe_subscription"></NoSubCard>

            <div
                v-else
                class="relative min-h-full flex-col rounded-2xl bg-surface-page"
            >
                <div class="mx-16 py-48 lg:mx-32">
                    <h1 class="fs-md mb-24 font-bold">Scheduled posts</h1>

                    <FullCalendarWrapper />
                </div>
            </div>
        </main>
    </DashboardLayout>
</template>

<script setup>
import DashboardLayout from "@/Layouts/DashboardLayout.vue";
import FullCalendarWrapper from "@/Components/Calendars/FullCalendarWrapper.vue";

const props = defineProps({
    auth: Object,
});

// TODO: Retrieve scheduled posts from the server/ayrshare API and pass them to FullCalendarWrapper
</script>
