<?php

use App\Adaptors\RAG\ChatBeesAdapter;

return [
    /*
    |--------------------------------------------------------------------------
    | Default RAG Service Provider
    |--------------------------------------------------------------------------
    |
    | This option controls the default RAG service provider that will be used
    | when no specific provider is requested.
    |
    */
    'default' => env('RAG_SERVICE_PROVIDER', 'chatbees'),

    /*
    |--------------------------------------------------------------------------
    | RAG Service Provider Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each RAG service
    | provider that your application will use. Each provider has its own
    | driver with appropriate configurations.
    |
    */
    'connections' => [
        'chatbees' => [
            'driver' => ChatBeesAdapter::class,
            'api_key' => env('CHATBEES_API_KEY'),
            'account_id' => env('CHATBEES_ACCOUNT_ID'),
            'base_url' => env('CHATBEES_BASE_URL'),
        ],
    ],
];
