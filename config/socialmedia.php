<?php

use App\Adaptors\SocialMedia\AyrShareAdapter;
use App\Adaptors\SocialMedia\SocialPilotAdapter;

return [
    /*
    |--------------------------------------------------------------------------
    | Default Social Media Platform
    |--------------------------------------------------------------------------
    |
    | This option controls the default social media platform that will be used
    | when no specific platform is requested.
    |
    */
    'default' => env('SOCIAL_MEDIA_PLATFORM', 'ayrshare'),

    /*
    |--------------------------------------------------------------------------
    | Social Media Platform Connections
    |--------------------------------------------------------------------------
    |
    | Here you may configure the connection information for each social media
    | platform that your application will use. Each platform has its own
    | driver with appropriate configurations.
    |
    */
    'connections' => [
        'socialpilot' => [
            'driver' => SocialPilotAdapter::class,
            'api_key' => env('SOCIALPILOT_API_KEY'),
            'base_url' => env('SOCIALPILOT_BASE_URL', 'https://api.socialpilot.co/v1'),
            'main_url' => env('SOCIALPILOT_MAIN_URL', 'https://social.pravi.ai'),
        ],
        'ayrshare' => [
            'driver' => AyrShareAdapter::class,
            'api_key' => env('AYRSHARE_API_KEY'),
            'base_url' => env('AYRSHARE_BASE_URL', 'https://api.ayrshare.com/api'),
            'domain' => env('AYRSHARE_DOMAIN'),
            'private_key' => env('AYRSHARE_PRIVATE_KEY'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Token Expiration Settings
    |--------------------------------------------------------------------------
    |
    | Configure token expiration durations for each platform.
    | Values are in minutes.
    |
    */
    'token_expiration' => [
        'ayrshare' => env('AYRSHARE_TOKEN_EXPIRATION', 5),
        'socialpilot' => env('SOCIALPILOT_TOKEN_EXPIRATION', 720), 
        'default' => env('DEFAULT_TOKEN_EXPIRATION', 720),
    ],
];
