<?php

return [
    'default_disk' => env('MEDIA_DEFAULT_DISK', 'public'),

    'use_social_media_storage' => env('MEDIA_USE_SOCIAL_MEDIA_STORAGE', true),

    'social_media_storage' => [
        'store_locally' => env('MEDIA_SOCIAL_STORE_LOCALLY', false),
        'local_disk' => env('MEDIA_SOCIAL_LOCAL_DISK', 'public'),
        'adapter' => env('MEDIA_SOCIAL_ADAPTER', 'ayrshare'),
    ],

    'supported_disks' => [
        'public',
        's3',
        'local',
    ],

    'max_file_size' => env('MEDIA_MAX_FILE_SIZE', 51200),

    'allowed_mime_types' => [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'video/mp4',
        'video/mpeg',
        'video/quicktime',
        'video/x-msvideo',
    ],
];
