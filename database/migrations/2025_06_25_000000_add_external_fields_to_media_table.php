<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('media', function (Blueprint $table) {
            $table->string('external_id')->nullable()->after('is_private');
            $table->string('external_url')->nullable()->after('external_id');

            $table->index('external_id');
        });
    }

    public function down(): void
    {
        Schema::table('media', function (Blueprint $table) {
            $table->dropIndex(['external_id']);
            $table->dropColumn(['external_id', 'external_url']);
        });
    }
};
