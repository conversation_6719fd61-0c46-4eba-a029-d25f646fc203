<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_rag_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->cascadeOnDelete();
            $table->string('provider');
            $table->string('collection_name')->nullable();
            $table->string('collection_id')->nullable();
            // API credentials will be fetched from config/environment
            $table->string('api_key')->nullable();
            $table->string('account_id')->nullable();
            $table->json('collection_metadata')->nullable();
            $table->json('settings')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            // Update unique constraint to include collection_name
            $table->unique(['user_id', 'provider', 'collection_name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_rag_accounts');
    }
};
