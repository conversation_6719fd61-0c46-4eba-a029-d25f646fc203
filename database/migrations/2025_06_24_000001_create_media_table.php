<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('media', function (Blueprint $table) {
            $table->id();
            $table->char('uuid', 36)->unique();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade');
            $table->string('path');
            $table->string('name');
            $table->string('disk');
            $table->string('mime')->nullable();
            $table->unsignedBigInteger('size');
            $table->boolean('is_private')->default(false);
            $table->timestamps();

            $table->index(['user_id', 'is_private']);
            $table->index('disk');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('media');
    }
};
