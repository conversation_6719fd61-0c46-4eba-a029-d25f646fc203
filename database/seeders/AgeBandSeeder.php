<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\AgeBand;

class AgeBandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $ageBands = [
            '18-24', '25-29', '30-34', '35-39', '40-44', '45-49', 
            '50-54', '55-59', '60-64', '65+', 'All'
        ];

        if (AgeBand::count() > 0) {
            return;
        }

        foreach ($ageBands as $band) {
            AgeBand::create(['range' => $band]);
        }
    }
}
