<?php

namespace Database\Seeders;

use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Models\Prompt;
use Illuminate\Database\Seeder;

class PromptSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        if (Prompt::count() > 0) {
            return;
        }

        $promptData = [
            [
                'name' => 'Social Media Content Generation',
                'type' => PromptType::QUICK_POST,
                'platform' => SocialPlatform::FACEBOOK,
                'prompt_text' => config('prompts.social_media_content'),
            ],
            [
                'name' => 'Twitter/X Content Generation',
                'type' => PromptType::QUICK_POST,
                'platform' => SocialPlatform::TWITTER,
                'prompt_text' => config('prompts.social_media_content'),
            ],
            [
                'name' => 'Instagram Content Generation',
                'type' => PromptType::QUICK_POST,
                'platform' => SocialPlatform::INSTAGRAM,
                'prompt_text' => config('prompts.social_media_content'),
            ],
            [
                'name' => 'LinkedIn Content Generation',
                'type' => PromptType::QUICK_POST,
                'platform' => SocialPlatform::LINKEDIN,
                'prompt_text' => config('prompts.social_media_content'),
            ],
            [
                'name' => 'Awareness Campaign - Facebook',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::FACEBOOK,
                'prompt_text' => config('prompts.awareness'),
            ],
            [
                'name' => 'Awareness Campaign - Twitter/X',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::TWITTER,
                'prompt_text' => config('prompts.awareness'),
            ],
            [
                'name' => 'Awareness Campaign - Instagram',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::INSTAGRAM,
                'prompt_text' => config('prompts.awareness'),
            ],
            [
                'name' => 'Awareness Campaign - LinkedIn',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::LINKEDIN,
                'prompt_text' => config('prompts.awareness'),
            ],
            [
                'name' => 'Stewardship Campaign - Facebook',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::FACEBOOK,
                'prompt_text' => config('prompts.stewardship'),
            ],
            [
                'name' => 'Donations Campaign - Facebook',
                'type' => PromptType::CAMPAIGN,
                'platform' => SocialPlatform::FACEBOOK,
                'prompt_text' => config('prompts.donations'),
            ],
        ];

        foreach ($promptData as $data) {
            Prompt::updateOrCreate(
                [
                    'type' => $data['type']->value,
                    'platform' => $data['platform']->value,
                ],
                [
                    'name' => $data['name'],
                    'prompt_text' => $data['prompt_text'],
                ]
            );
        }
    }
}
