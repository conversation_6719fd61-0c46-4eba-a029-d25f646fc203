<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Gender;

class GenderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $genders = ['Female', 'Male', 'Unknown', 'All'];

        if (Gender::count() > 0) {
            return;
        }

        foreach ($genders as $gender) {
            Gender::create(['gender' => $gender]);
        }
    }
}
