{"private": true, "type": "module", "scripts": {"start:dev": "docker compose up -d  && ./vendor/bin/sail npm run dev", "dev": "vite", "build": "vite build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "devDependencies": {"@chromatic-com/storybook": "^1.6.1", "@inertiajs/vue3": "^1.0.14", "@storybook/addon-essentials": "^8.2.9", "@storybook/addon-interactions": "^8.2.9", "@storybook/addon-links": "^8.2.9", "@storybook/addon-onboarding": "^8.2.9", "@storybook/blocks": "^8.2.9", "@storybook/test": "^8.2.9", "@storybook/vue3": "^8.2.9", "@storybook/vue3-vite": "^8.2.9", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.15", "@vitejs/plugin-vue": "^5.0.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.16", "axios": "^1.6.4", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.41", "postcss-import": "^16.1.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "storybook": "^8.2.9", "tailwindcss": "^3.4.0", "typescript": "^5.8.3", "vite": "^5.0", "vite-plugin-vue-devtools": "^7.7.6", "vue": "^3.3.13", "vue-tsc": "^2.2.8"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@fullcalendar/vue3": "^6.1.17", "@vuepic/vue-datepicker": "^11.0.2", "apexcharts": "^4.1.0", "date-fns": "^4.1.0", "filepond": "^4.32.7", "filepond-plugin-file-encode": "^2.1.14", "filepond-plugin-file-poster": "^2.5.2", "filepond-plugin-file-validate-size": "^2.2.8", "filepond-plugin-file-validate-type": "^1.2.9", "filepond-plugin-image-preview": "^4.6.12", "floating-vue": "^5.2.2", "lodash": "^4.17.21", "openai": "^4.73.0", "pinia": "^2.2.4", "showdown": "^2.1.0", "swiper": "^11.2.8", "vue-filepond": "^7.0.4", "vue-iconsax": "^2.0.0", "vue-select": "^4.0.0-beta.6", "vue3-apexcharts": "^1.8.0"}}