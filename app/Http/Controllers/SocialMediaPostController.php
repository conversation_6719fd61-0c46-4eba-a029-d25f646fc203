<?php

namespace App\Http\Controllers;

use App\Contracts\Services\SocialMediaServiceInterface;
use App\Enums\SocialPlatform;
use App\Models\SocialMediaPost;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Enum;
use Inertia\Inertia;

class SocialMediaPostController extends Controller
{
    protected SocialMediaServiceInterface $socialMediaService;

    public function __construct(SocialMediaServiceInterface $socialMediaService)
    {
        $this->socialMediaService = $socialMediaService;
    }

    /**
     * Display a listing of the posts.
     */
    public function index()
    {
        $posts = SocialMediaPost::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('SocialMedia/Index', [
            'posts' => $posts,
            'platforms' => SocialPlatform::platformsForFrontend(),
        ]);
    }

    /**
     * Show the form for creating a new post.
     */
    public function create()
    {
        return Inertia::render('SocialMedia/Create', [
            'platforms' => SocialPlatform::platformsForFrontend(),
        ]);
    }

    /**
     * Store a newly created post in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'content' => 'required|string',
            'platforms' => 'required|array|min:1',
            'platforms.*' => [new Enum(SocialPlatform::class)],
            'media_urls' => 'nullable|array',
            'media_urls.*' => 'url',
            'scheduled_at' => 'nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $postData = [
            'content' => $request->input('content'),
            'platforms' => $request->input('platforms'),
            'media_urls' => $request->input('media_urls') ?? [],
            'scheduled_at' => $request->input('scheduled_at'),
        ];

        $post = $this->socialMediaService->publishPost($postData, Auth::user());

        if ($post) {
            $message = $post->scheduled_at
                ? 'Post scheduled successfully for '.$post->scheduled_at->format('M d, Y \a\t h:i A')
                : 'Post published successfully!';

            return redirect()->route('social-media-posts.index')
                ->with('success', $message);
        }

        return redirect()->back()
            ->with('error', 'Failed to publish post. Please try again.')
            ->withInput();
    }

    /**
     * Display the specified post.
     */
    public function show(SocialMediaPost $socialMediaPost)
    {
        if ($socialMediaPost->user_id !== Auth::id()) {
            abort(403);
        }

        // Get platform labels for display
        $platformLabels = $socialMediaPost->getPlatformLabels();

        return Inertia::render('SocialMedia/Show', [
            'post' => $socialMediaPost,
            'platformLabels' => $platformLabels,
        ]);
    }

    /**
     * Remove the specified post from storage.
     */
    public function destroy(SocialMediaPost $socialMediaPost)
    {
        if ($socialMediaPost->user_id !== Auth::id()) {
            abort(403);
        }

        $success = $this->socialMediaService->deletePost($socialMediaPost);

        if ($success) {

            $message = 'Post deleted successfully!';

            $unsupportedPlatforms = array_intersect(
                $socialMediaPost->platforms,
                ['instagram', 'tiktok', 'facebook']
            );

            if (! empty($unsupportedPlatforms)) {
                $platformNames = array_map(function ($platform) {
                    return ucfirst($platform);
                }, $unsupportedPlatforms);

                $message .= ' Note: '.implode(', ', $platformNames).' posts may need to be manually deleted using their respective apps.';
            }

            $socialMediaPost->delete();

            return redirect()->route('social-media-posts.index')
                ->with('success', $message);
        }

        return redirect()->back()
            ->with('error', 'Failed to delete post. Please try again or delete manually from the social platforms.');
    }
}
