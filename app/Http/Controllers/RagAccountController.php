<?php

namespace App\Http\Controllers;

use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Enums\RagServiceProviderType;
use App\Models\UserRagAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class RagAccountController extends Controller
{
    /**
     * @var RagServiceInterface
     */
    protected $ragService;

    /**
     * @var UserRagAccountServiceInterface
     */
    protected $userRagAccountService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        RagServiceInterface $ragService,
        UserRagAccountServiceInterface $userRagAccountService
    ) {
        $this->middleware('auth');
        $this->ragService = $ragService;
        $this->userRagAccountService = $userRagAccountService;
    }

    /**
     * Display a listing of the user's RAG accounts.
     */
    public function index()
    {
        $user = Auth::user();
        $accounts = $user->ragAccounts;

        return view('rag.accounts.index', [
            'accounts' => $accounts,
            'providers' => RagServiceProviderType::cases(),
        ]);
    }

    /**
     * Show the form for creating a new RAG account.
     */
    public function create()
    {
        return view('rag.accounts.create', [
            'providers' => RagServiceProviderType::cases(),
        ]);
    }

    /**
     * Store a newly created RAG account.
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'provider' => ['required', 'string', 'in:'.implode(',', RagServiceProviderType::values())],
            'api_key' => ['required', 'string'],
            'account_id' => ['required', 'string'],
            'collection_name' => ['nullable', 'string', 'max:50'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $provider = RagServiceProviderType::from($request->input('provider'));

        try {
            $account = $this->ragService->createUserRagAccount(
                $user,
                $provider,
                [
                    'api_key' => $request->input('api_key'),
                    'account_id' => $request->input('account_id'),
                    'collection_name' => $request->input('collection_name'),
                ]
            );

            return redirect()->route('rag.accounts.index')
                ->with('success', 'RAG service account has been linked successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withErrors(['error' => 'Failed to create RAG account: '.$e->getMessage()])
                ->withInput();
        }
    }

    /**
     * Display the specified RAG account.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $account = UserRagAccount::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $documents = $this->userRagAccountService->getDocuments($account);

        return view('rag.accounts.show', [
            'account' => $account,
            'documents' => $documents,
        ]);
    }

    /**
     * Show the form for editing the specified RAG account.
     */
    public function edit(string $id)
    {
        $user = Auth::user();
        $account = UserRagAccount::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        return view('rag.accounts.edit', [
            'account' => $account,
            'providers' => RagServiceProviderType::cases(),
        ]);
    }

    /**
     * Update the specified RAG account.
     */
    public function update(Request $request, string $id)
    {
        $user = Auth::user();
        $account = UserRagAccount::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $validator = Validator::make($request->all(), [
            'api_key' => ['required', 'string'],
            'account_id' => ['required', 'string'],
            'is_active' => ['boolean'],
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $this->userRagAccountService->updateAccountData($account, [
            'api_key' => $request->input('api_key'),
            'account_id' => $request->input('account_id'),
            'is_active' => $request->has('is_active'),
        ]);

        return redirect()->route('rag.accounts.index')
            ->with('success', 'RAG service account has been updated successfully.');
    }

    /**
     * Deactivate the specified RAG account.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();
        $account = UserRagAccount::where('id', $id)
            ->where('user_id', $user->id)
            ->firstOrFail();

        $this->userRagAccountService->updateAccountData($account, [
            'is_active' => false,
        ]);

        return redirect()->route('rag.accounts.index')
            ->with('success', 'RAG service account has been deactivated successfully.');
    }
}
