<?php

namespace App\Http\Controllers;

use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Models\UserRagAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;

class RagChatController extends Controller
{
    /**
     * @var RagServiceInterface
     */
    protected $ragService;

    /**
     * @var UserRagAccountServiceInterface
     */
    protected $userRagAccountService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        RagServiceInterface $ragService,
        UserRagAccountServiceInterface $userRagAccountService
    ) {
        $this->ragService = $ragService;
        $this->userRagAccountService = $userRagAccountService;
    }

    /**
     * Display the RAG chat interface.
     */
    public function index()
    {
        $user = Auth::user();

        $collections = $user->ragAccounts()
            ->where('is_active', true)
            ->get();

        if ($collections->isEmpty()) {
            Log::warning('No active RAG collections found for user #'.$user->id);
        } else {
            foreach ($collections as $collection) {
                $documentCount = $collection->documents()->count();
            }
        }

        return Inertia::render('RAG/Chat', [
            'collections' => $collections->map(function ($collection) {
                return [
                    'id' => $collection->id,
                    'name' => $collection->collection_name ?? 'Unnamed Collection',
                    'provider' => $collection->provider,
                    'document_count' => $collection->documents()->count(),
                ];
            }),
        ]);
    }

    /**
     * Process a chat request with the RAG service.
     */
    public function chat(Request $request)
    {
        $request->validate([
            'question' => 'required|string|max:1000',
            'collection_id' => 'required|exists:user_rag_accounts,id',
            'document_ids' => 'sometimes|nullable|array',
            'document_ids.*' => 'string',
            'history_messages' => 'sometimes|array',
        ]);

        $user = Auth::user();
        $collectionId = $request->input('collection_id');
        $documentIds = $request->input('document_ids', []);
        $question = $request->input('question');
        $historyMessages = $request->input('history_messages', []);

        $collection = UserRagAccount::where('id', $collectionId)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->firstOrFail();

        $response = [];

        if (! empty($documentIds)) {
            foreach ($documentIds as $documentId) {
                try {
                    $documentResponse = $this->ragService->ask(
                        $user,
                        $question,
                        $documentId,
                        $historyMessages,
                        $collection->provider,
                        $collection->id
                    );

                    if (! isset($response['answer'])) {
                        $response = $documentResponse;
                    } else {
                        if (isset($documentResponse['sources'])) {
                            $response['sources'] = array_merge(
                                $response['sources'] ?? [],
                                $documentResponse['sources'] ?? []
                            );
                        }
                    }
                } catch (\Exception $e) {
                    Log::error('Error processing document', [
                        'document_id' => $documentId,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);

                    if (empty($response)) {
                        return response()->json([
                            'error' => true,
                            'detail' => 'Error processing document: '.$e->getMessage(),
                        ], 500);
                    }
                }
            }

            if (empty($response)) {
                return response()->json([
                    'error' => true,
                    'detail' => 'Could not generate a response from the provided documents.',
                ], 500);
            }
        } else {

            try {
                $response = $this->ragService->ask(
                    $user,
                    $question,
                    null,
                    $historyMessages,
                    $collection->provider,
                    $collection->id
                );
            } catch (\Exception $e) {
                Log::error('Error querying all documents', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return response()->json([
                    'error' => true,
                    'detail' => 'Error processing request: '.$e->getMessage(),
                ], 500);
            }
        }

        return response()->json($response);
    }

    /**
     * Get documents for a specific RAG collection.
     */
    public function getDocuments(Request $request)
    {
        $request->validate([
            'collection_id' => 'required|exists:user_rag_accounts,id',
        ]);

        $user = Auth::user();
        $collectionId = $request->input('collection_id');


        $collection = UserRagAccount::where('id', $collectionId)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->firstOrFail();

        $documents = $this->userRagAccountService->getDocuments($collection);

        $transformedDocuments = $documents->map(function ($document) {
            return [
                'id' => $document->id,
                'uuid' => $document->uuid,
                'name' => $document->original_filename,
                'status' => $document->status,
                'created_at' => $document->created_at,
                'updated_at' => $document->updated_at,
            ];
        });

        return response()->json([
            'documents' => $transformedDocuments,
        ]);
    }
}
