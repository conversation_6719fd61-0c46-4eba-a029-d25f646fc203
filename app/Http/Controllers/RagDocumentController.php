<?php

namespace App\Http\Controllers;

use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Http\Requests\UploadRagDocumentRequest;
use App\Jobs\ProcessRagDocumentUpload;
use App\Models\RagDocument;
use App\Models\UserRagAccount;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;

class RagDocumentController extends Controller
{
    /**
     * @var RagServiceInterface
     */
    protected $ragService;

    /**
     * @var UserRagAccountServiceInterface
     */
    protected $userRagAccountService;

    /**
     * Create a new controller instance.
     */
    public function __construct(
        RagServiceInterface $ragService,
        UserRagAccountServiceInterface $userRagAccountService
    ) {
        $this->ragService = $ragService;
        $this->userRagAccountService = $userRagAccountService;
    }

    /**
     * Display a listing of the user's RAG documents.
     */
    public function index(Request $request)
    {
        $user = Auth::user();
        $collections = $user->ragAccounts()->where('is_active', true)->get();

        $selectedCollectionId = $request->input('collection_id');
        $selectedCollection = null;

        if ($selectedCollectionId) {
            $selectedCollection = $collections->firstWhere('id', $selectedCollectionId);
        }

        if (! $selectedCollection && $collections->isNotEmpty()) {
            $selectedCollection = $collections->first();
        }

        $documents = collect();

        if ($selectedCollection) {
            $documents = $this->userRagAccountService->getDocuments($selectedCollection);

            if ($documents->isNotEmpty()) {
                Log::info('First document: '.$documents->first()->original_filename.' (ID: '.$documents->first()->id.')');
            } else {
                Log::warning('No documents found for collection #'.$selectedCollection->id);
            }
        }

        return Inertia::render('RAG/Documents/Index', [
            'collections' => $collections,
            'selectedCollection' => $selectedCollection,
            'documents' => $documents,
        ]);
    }

    /**
     * Show the form for uploading a new document.
     */
    public function create()
    {
        $user = Auth::user();
        $collections = $user->ragAccounts()->where('is_active', true)->get();

        return Inertia::render('RAG/Documents/Create', [
            'accounts' => $collections, // Keep as 'accounts' for now to avoid breaking existing code
        ]);
    }

    /**
     * Store a newly created document in storage and queue upload.
     */
    public function store(UploadRagDocumentRequest $request)
    {
        $user = Auth::user();

        // Use the validated data from the form request
        $validated = $request->validated();
        $file = $validated['document'];
        $collectionId = $validated['collection_id'];
        $title = $validated['title'] ?? null;
        $metadata = $validated['metadata'] ?? [];

        // Add title to metadata if provided
        if ($title) {
            $metadata['title'] = $title;
        }

        // Validate that the collection belongs to the user
        $collection = UserRagAccount::where('id', $collectionId)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->firstOrFail();

        // Create the document record through the service
        $document = $this->userRagAccountService->addDocument(
            $collection,
            $file->getPathname(),
            $file->getClientOriginalName(),
            $metadata
        );

        if (! $document) {
            return redirect()->back()
                ->withErrors([
                    'document' => 'A document with this name already exists. Please rename the file and try again.',
                ])
                ->withInput();
        }

        // Queue the document for processing if needed
        if ($document->status !== 'completed') {
            ProcessRagDocumentUpload::dispatch($document);
        }

        return redirect()->route('rag.documents.index', ['collection_id' => $collectionId])
            ->with('success', 'Document has been uploaded and is being processed.');
    }

    /**
     * Display the specified document.
     */
    public function show(string $id)
    {
        $user = Auth::user();
        $document = RagDocument::whereHas('ragAccount', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('uuid', $id)->firstOrFail();

        // Get outlines and FAQs if not already fetched
        if ((! $document->outlines || ! $document->faqs) && $document->status === 'completed') {
            $outlineFaq = $this->ragService->getDocumentOutlineFaq(
                $user,
                $document->uuid
            );

            $document->update([
                'outlines' => $outlineFaq['outlines'] ?? [],
                'faqs' => $outlineFaq['faqs'] ?? [],
            ]);

            $document->refresh();
        }

        return Inertia::render('RAG/Documents/Show', [
            'document' => $document,
        ]);
    }

    /**
     * Ask a question about a document.
     */
    public function ask(Request $request, string $id)
    {
        $user = Auth::user();
        $document = RagDocument::whereHas('ragAccount', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('uuid', $id)->firstOrFail();

        $request->validate([
            'question' => 'required|string|max:1000',
        ]);

        $question = $request->input('question');
        $historyMessages = $request->input('history_messages', []);

        $response = $this->ragService->ask(
            $user,
            $question,
            $document->uuid,
            $historyMessages,
            null,
            $document->user_rag_account_id
        );

        return response()->json($response);
    }

    /**
     * Ask a question about all documents in a collection.
     */
    public function askAll(Request $request)
    {
        $user = Auth::user();
        $collectionId = $request->input('collection_id');

        $request->validate([
            'question' => 'required|string|max:1000',
            'collection_id' => 'required|exists:user_rag_accounts,id',
        ]);

        // Validate that the collection belongs to the user
        $collection = UserRagAccount::where('id', $collectionId)
            ->where('user_id', $user->id)
            ->where('is_active', true)
            ->firstOrFail();

        $question = $request->input('question');
        $historyMessages = $request->input('history_messages', []);

        $response = $this->ragService->ask(
            $user,
            $question,
            null, // No specific document ID, query all documents
            $historyMessages,
            $collection->provider,
            $collection->id
        );

        return response()->json($response);
    }

    /**
     * Remove the specified document from storage.
     */
    public function destroy(string $id)
    {
        $user = Auth::user();
        $document = RagDocument::whereHas('ragAccount', function ($query) use ($user) {
            $query->where('user_id', $user->id);
        })->where('uuid', $id)->firstOrFail();

        $collectionId = $document->user_rag_account_id;

        // Delete from the RAG service
        if ($document->status === 'completed' && $document->document_id) {
            $adapter = $this->ragService->getAdapter($document->ragAccount->provider);

            if ($adapter) {
                $adapter->deleteDocument(
                    $document->ragAccount->collection_id,
                    $document->document_id
                );
            }
        }

        // Delete from storage
        if ($document->file_path) {
            Storage::delete($document->file_path);
        }

        // Delete the record
        $document->delete();

        return redirect()->route('rag.documents.index', ['collection_id' => $collectionId])
            ->with('success', 'Document has been deleted successfully.');
    }
}
