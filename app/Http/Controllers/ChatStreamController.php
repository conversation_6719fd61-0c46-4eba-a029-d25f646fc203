<?php

namespace App\Http\Controllers;

use App\Enums\AIProvider;
use App\Http\Requests\ChatStreamRequest;
use App\Services\V1\AIService\AssistantService;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ChatStreamController extends Controller
{
    protected AssistantService $assistantService;

    public function __construct(AssistantService $assistantService)
    {
        $this->assistantService = $assistantService;
    }

    /**
     * Stream chat response using Server-Sent Events
     */
    public function stream(ChatStreamRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json([
                'error' => 'User or company not found',
            ], 404);
        }

        $validated = $request->validated();

        $this->assistantService->useProvider(AIProvider::OpenAI->value);

        if (! $this->assistantService->supportsStreaming()) {
            return response()->json([
                'error' => 'Current AI provider does not support streaming',
                'provider' => $this->assistantService->getCurrentProvider(),
            ], 400);
        }

        return new StreamedResponse(function () use ($validated, $company) {
            echo 'data: '.json_encode([
                'type' => 'start',
                'message' => 'Starting content generation...',
                'provider' => $this->assistantService->getCurrentProvider(),
            ])."\n\n";

            if (ob_get_level()) {
                ob_end_flush();
            }
            flush();

            try {
                $messages = $this->assistantService->prepareMessages($validated, $company);

                $this->assistantService->streamResponse($messages, function ($chunk) {
                    echo 'data: '.json_encode([
                        'type' => 'chunk',
                        'content' => $chunk,
                    ])."\n\n";

                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                });

                echo 'data: '.json_encode([
                    'type' => 'complete',
                    'provider' => $this->assistantService->getCurrentProvider(),
                ])."\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();

            } catch (\Exception $e) {
                echo 'data: '.json_encode([
                    'type' => 'error',
                    'message' => 'Failed to generate content: '.$e->getMessage(),
                    'provider' => $this->assistantService->getCurrentProvider(),
                ])."\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Cache-Control',
        ]);
    }

    /**
     * Handle regular non-streaming chat requests (fallback)
     */
    public function chat(ChatStreamRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json([
                'error' => 'User or company not found',
            ], 404);
        }

        $validated = $request->validated();

        $this->assistantService->useProvider(AIProvider::OpenAI->value);

        try {
            $messages = $this->assistantService->prepareMessages($validated, $company);
            $response = $this->assistantService->getNonStreamResponse($messages);

            return response()->json([
                'message' => $response,
                'timestamp' => now()->toISOString(),
                'provider' => $this->assistantService->getCurrentProvider(),
                'provider_info' => $this->assistantService->getProviderInfo(),
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process request',
                'details' => $e->getMessage(),
                'provider' => $this->assistantService->getCurrentProvider(),
            ], 500);
        }
    }

    /**
     * Get available AI providers and current provider info
     */
    public function providers()
    {
        return response()->json([
            'current' => $this->assistantService->getCurrentProvider(),
            'available' => $this->assistantService->getAvailableProviders(),
            'provider_info' => $this->assistantService->getProviderInfo(),
        ]);
    }
}
