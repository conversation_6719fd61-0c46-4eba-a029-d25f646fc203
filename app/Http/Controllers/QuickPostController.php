<?php

namespace App\Http\Controllers;

use App\Enums\AIProvider;
use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Http\Requests\QuickPostRequest;
use App\Services\V1\AIService\AssistantService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class QuickPostController extends Controller
{
    protected AssistantService $assistantService;

    public function __construct(AssistantService $assistantService)
    {
        $this->assistantService = $assistantService;
    }

    public function stream(QuickPostRequest $request)
    {
        $user = Auth::user();
        $company = $user->getCompany();

        if (! $user || ! $company) {
            return response()->json([
                'error' => 'User or company not found',
            ], 404);
        }

        $validated = $request->validated();
        $platform = SocialPlatform::from(strtolower($validated['selected_social_platform']));

        $promptText = getPromptText(PromptType::QUICK_POST, $platform);
        if (! $promptText) {
            return response()->json([
                'error' => 'No prompt found for this platform',
                'platform' => $platform->label(),
            ], 404);
        }

        $this->assistantService->useProvider(AIProvider::OpenAI->value);

        $requestData = $this->prepareRequestData($validated);

        if (! $this->assistantService->supportsStreaming()) {
            return $this->generateNonStreaming($requestData, $company);
        }

        return $this->generateStreaming($requestData, $company);
    }

    private function prepareRequestData(array $validated): array
    {
        return [
            'feature' => 'social_media_content',
            'selected_social_platform' => $validated['selected_social_platform'],
            'campaign_type' => 'quick_post',
            'tone' => 'professional',
            'audience_gender' => 'diverse audience',
            'message' => 'Generate engaging social media content for the specified platform.',
        ];
    }

    private function generateStreaming(array $requestData, $company): StreamedResponse
    {
        return new StreamedResponse(function () use ($requestData, $company) {
            echo 'data: '.json_encode([
                'type' => 'start',
                'message' => 'Generating quick post...',
                'provider' => $this->assistantService->getCurrentProvider(),
            ])."\n\n";

            if (ob_get_level()) {
                ob_end_flush();
            }
            flush();

            try {
                $messages = $this->assistantService->prepareMessages($requestData, $company);

                $this->assistantService->streamResponse($messages, function ($chunk) {
                    echo 'data: '.json_encode([
                        'type' => 'chunk',
                        'content' => $chunk,
                    ])."\n\n";

                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();
                });

                echo 'data: '.json_encode([
                    'type' => 'complete',
                    'provider' => $this->assistantService->getCurrentProvider(),
                ])."\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();

            } catch (\Exception $e) {
                echo 'data: '.json_encode([
                    'type' => 'error',
                    'message' => 'Failed to generate content: '.$e->getMessage(),
                    'provider' => $this->assistantService->getCurrentProvider(),
                ])."\n\n";

                if (ob_get_level()) {
                    ob_flush();
                }
                flush();
            }
        }, 200, [
            'Content-Type' => 'text/event-stream',
            'Cache-Control' => 'no-cache',
            'Connection' => 'keep-alive',
            'X-Accel-Buffering' => 'no',
            'Access-Control-Allow-Origin' => '*',
            'Access-Control-Allow-Headers' => 'Cache-Control',
        ]);
    }

    private function generateNonStreaming(array $requestData, $company)
    {
        try {
            $messages = $this->assistantService->prepareMessages($requestData, $company);
            $response = $this->assistantService->getNonStreamResponse($messages);

            return response()->json([
                'content' => $response,
                'timestamp' => now()->toISOString(),
                'provider' => $this->assistantService->getCurrentProvider(),
                'platform' => $requestData['selected_social_platform'],
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to generate quick post',
                'details' => $e->getMessage(),
                'provider' => $this->assistantService->getCurrentProvider(),
            ], 500);
        }
    }
}
