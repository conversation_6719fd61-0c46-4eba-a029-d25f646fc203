<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\GetMediaGalleryRequest;
use App\Http\Requests\GetMediaUrlRequest;
use App\Http\Requests\GetPostsRequest;
use App\Http\Requests\PublishPostRequest;
use App\Http\Requests\StoreMediaFromUrlRequest;
use App\Http\Requests\UploadMediaRequest;
use App\Http\Resources\MediaResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SocialMediaController extends Controller
{
    public function getSocialUrl(Request $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $result = socialMediaService()->getSocialUrl(socialMediaService()->generateToken($user));

        if (! $result) {
            return response()->json(['error' => 'Failed to generate social media token'], 500);
        }

        return response()->json(
            [
                'data' => [
                    'url' => $result,
                ],
                'success' => true,
                'message' => 'Social media URL generated successfully',
            ]
        );
    }

    public function publishPost(PublishPostRequest $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $postData = $this->preparePostData($request->validated());

            $hasConnectedAccount = $this->checkUserSocialConnection($user);
            if (! $hasConnectedAccount) {
                return $this->handleSocialConnectionRequired($user);
            }

            $post = socialMediaService()->publishPost($postData, $user);

            if (! $post) {
                $errorDetails = $this->analyzePlatformErrors($user, $postData['platforms']);

                if ($errorDetails['requiresConnection']) {
                    return $this->handleSocialConnectionRequired($user, $errorDetails['platforms']);
                }

                return response()->json([
                    'error' => 'Failed to publish post',
                    'details' => $errorDetails['message'],
                    'platform_errors' => $errorDetails['platformErrors'],
                ], 500);
            }

            return response()->json([
                'data' => [
                    'id' => $post->id,
                    'post_id' => $post->post_group_id,
                    'status' => $post->status,
                    'scheduled_at' => $post->scheduled_at,
                    'platforms' => $post->platforms,
                    'content' => $post->content,
                ],
                'success' => true,
                'message' => $post->scheduled_at ? 'Post scheduled successfully' : 'Post published successfully',
            ]);

        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Platform linkage errors detected') === 0) {
                return $this->handlePlatformLinkageErrors($e, $user);
            }

            $connectionErrors = [
                'Unauthorized:',
                'Forbidden:',
                'Profile not found:',
            ];

            foreach ($connectionErrors as $errorPattern) {
                if (strpos($e->getMessage(), $errorPattern) === 0) {
                    return $this->handleSocialConnectionRequired($user);
                }
            }

            if ($this->isSocialConnectionError($e)) {
                return $this->handleSocialConnectionRequired($user);
            }

            return response()->json([
                'error' => 'An error occurred while processing your request',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    private function checkUserSocialConnection($user): bool
    {
        $defaultPlatform = config('socialmedia.default', 'ayrshare');

        $userSocialAccount = $user->socialAccounts()
            ->where('platform', $defaultPlatform)
            ->where('is_active', true)
            ->first();

        return $userSocialAccount && $userSocialAccount->platform_user_id;
    }

    private function analyzePlatformErrors($user, array $platforms): array
    {
        $platformErrors = [];
        $requiresConnection = false;
        $defaultPlatform = config('socialmedia.default', 'ayrshare');

        $userSocialAccount = $user->socialAccounts()
            ->where('platform', $defaultPlatform)
            ->where('is_active', true)
            ->first();

        if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
            $requiresConnection = true;
            $platformErrors[] = [
                'platform' => $defaultPlatform,
                'error' => 'User not connected to social media platform',
                'code' => 'NOT_CONNECTED',
            ];
        }

        foreach ($platforms as $platform) {
            $platformErrors[] = [
                'platform' => $platform,
                'error' => 'Failed to publish to '.ucfirst($platform),
                'code' => $requiresConnection ? 'NOT_CONNECTED' : 'PUBLISH_FAILED',
            ];
        }

        return [
            'requiresConnection' => $requiresConnection,
            'platforms' => $platforms,
            'platformErrors' => $platformErrors,
            'message' => $requiresConnection
                ? 'User must connect to social media platform before publishing'
                : 'Failed to publish to one or more platforms',
        ];
    }

    private function isSocialConnectionError(\Exception $e): bool
    {
        $message = strtolower($e->getMessage());
        $connectionKeywords = [
            'not connected',
            'no integration found',
            'unauthorized',
            'invalid token',
            'expired token',
            'authentication failed',
            'profile not found',
        ];

        foreach ($connectionKeywords as $keyword) {
            if (strpos($message, $keyword) !== false) {
                return true;
            }
        }

        return false;
    }

    private function handleSocialConnectionRequired($user, array $platforms = []): JsonResponse
    {
        try {
            $connectionUrl = null;
            $token = socialMediaService()->generateToken($user);

            if ($token) {
                $connectionUrl = socialMediaService()->getSocialUrl($token);
            }

            if (! $connectionUrl) {
                $tokenAndUrl = socialMediaService()->generateTokenAndUrl($user);
                $connectionUrl = $tokenAndUrl['url'] ?? null;
            }

            return response()->json([
                'error' => 'Social media connection required',
                'error_code' => 'SOCIAL_CONNECTION_REQUIRED',
                'message' => 'You need to connect your social media accounts before publishing posts.',
                'action_required' => 'CONNECT_SOCIAL_MEDIA',
                'connection_url' => $connectionUrl,
                'platforms_affected' => $platforms ?: ['all'],
                'instructions' => 'Click the connection URL to link your social media accounts, then try publishing again.',
            ], 403);

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Social media connection required',
                'error_code' => 'SOCIAL_CONNECTION_REQUIRED',
                'message' => 'You need to connect your social media accounts before publishing posts.',
                'action_required' => 'CONNECT_SOCIAL_MEDIA',
                'connection_url' => null,
                'instructions' => 'Please contact support to set up your social media connection.',
            ], 403);
        }
    }

    private function preparePostData(array $validated): array
    {
        $postData = [
            'content' => $validated['post'],
            'platforms' => $validated['platforms'],
        ];

        if (isset($validated['mediaUrls'])) {
            $postData['media_urls'] = $validated['mediaUrls'];
        }

        if (isset($validated['scheduleDate'])) {
            $postData['scheduled_at'] = $validated['scheduleDate'];
        }

        $optionalFields = [
            'profileKey', 'youTubeOptions', 'linkedInOptions', 'tiktokOptions',
            'shortenLinks', 'muteVideo', 'autoHashtag', 'boost', 'replyTo',
            'lat', 'long', 'altText', 'priority', 'poll', 'threadOptions',
        ];

        foreach ($optionalFields as $field) {
            if (isset($validated[$field])) {
                $postData[$field] = $validated[$field];
            }
        }

        return $postData;
    }

    public function uploadMedia(UploadMediaRequest $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $isPrivate = $request->boolean('is_private', false);

            $media = mediaService()->store(
                $request->file('file'),
                $user,
                null,
                $isPrivate
            );

            return response()->json([
                'data' => new MediaResource($media),
                'success' => true,
                'message' => 'Media uploaded successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while uploading media',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getUploadUrl(Request $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validator = Validator::make($request->all(), [
            'fileName' => 'required|string|max:255',
            'contentType' => 'nullable|string|in:mp4,mov,png,jpg,jpeg',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors(),
            ], 422);
        }

        try {
            $hasConnectedAccount = $this->checkUserSocialConnection($user);
            if (! $hasConnectedAccount) {
                return $this->handleSocialConnectionRequired($user);
            }

            $result = socialMediaService()->getUploadUrl(
                $request->input('fileName'),
                $request->input('contentType'),
                $user
            );

            if (! $result) {
                return response()->json([
                    'error' => 'Failed to get upload URL',
                    'message' => 'Unable to generate upload URL. Please try again.',
                ], 500);
            }

            return response()->json([
                'data' => [
                    'upload_url' => $result['upload_url'],
                    'media_id' => $result['media_id'] ?? null,
                    'expires_at' => $result['expires_at'] ?? null,
                ],
                'success' => true,
                'message' => 'Upload URL generated successfully',
            ]);
        } catch (\Exception $e) {
            if ($this->isSocialConnectionError($e)) {
                return $this->handleSocialConnectionRequired($user);
            }

            return response()->json([
                'error' => 'An error occurred while getting upload URL',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getMediaGallery(GetMediaGalleryRequest $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $filters = array_filter([
                'mime_type' => $request->input('mime_type'),
                'is_private' => $request->input('is_private'),
                'search' => $request->input('search'),
            ]);

            $perPage = $request->input('per_page', 15);
            $mediaPaginated = mediaService()->getUserMedia($user, $perPage, $filters);

            $mediaData = MediaResource::collection($mediaPaginated->items());

            return response()->json([
                'data' => $mediaData,
                'meta' => [
                    'current_page' => $mediaPaginated->currentPage(),
                    'last_page' => $mediaPaginated->lastPage(),
                    'per_page' => $mediaPaginated->perPage(),
                    'total' => $mediaPaginated->total(),
                    'from' => $mediaPaginated->firstItem(),
                    'to' => $mediaPaginated->lastItem(),
                ],
                'success' => true,
                'message' => 'Media gallery retrieved successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while retrieving media gallery',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function resizeMedia(Request $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validator = Validator::make($request->all(), [
            'mediaId' => 'required|string',
            'width' => 'required|integer|min:1|max:10000',
            'height' => 'required|integer|min:1|max:10000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors(),
            ], 422);
        }

        try {
            $result = socialMediaService()->resizeMedia(
                $request->input('mediaId'),
                $request->input('width'),
                $request->input('height'),
                $user
            );

            if (! $result) {
                return response()->json(['error' => 'Failed to resize media'], 500);
            }

            return response()->json([
                'data' => [
                    'media_id' => $result['media_id'],
                    'media_url' => $result['media_url'],
                    'width' => $result['width'],
                    'height' => $result['height'],
                ],
                'success' => true,
                'message' => 'Media resized successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while resizing media',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getMediaMetadata(Request $request, string $uuid): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $media = mediaService()->getByUuid($uuid);

            if (! $media || $media->user_id !== $user->id) {
                return response()->json(['error' => 'Media not found'], 404);
            }

            return response()->json([
                'data' => new MediaResource($media),
                'success' => true,
                'message' => 'Media metadata retrieved successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while retrieving media metadata',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function verifyMediaUrl(Request $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        $validator = Validator::make($request->all(), [
            'mediaUrl' => 'required|url',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'details' => $validator->errors(),
            ], 422);
        }

        try {
            $exists = socialMediaService()->verifyMediaUrl(
                $request->input('mediaUrl'),
                $user
            );

            return response()->json([
                'data' => ['exists' => $exists],
                'success' => true,
                'message' => $exists ? 'Media URL exists' : 'Media URL does not exist',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while verifying media URL',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function deleteMedia(Request $request, string $uuid): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $media = mediaService()->getByUuid($uuid);

            if (! $media || $media->user_id !== $user->id) {
                return response()->json(['error' => 'Media not found'], 404);
            }

            $success = mediaService()->delete($media);

            if (! $success) {
                return response()->json(['error' => 'Failed to delete media'], 500);
            }

            return response()->json([
                'success' => true,
                'message' => 'Media deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while deleting media',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    private function handlePlatformLinkageErrors(\Exception $e, $user): JsonResponse
    {
        try {
            $connectionUrl = null;
            $token = socialMediaService()->generateToken($user);

            if ($token) {
                $connectionUrl = socialMediaService()->getSocialUrl($token);
            }

            if (! $connectionUrl) {
                $tokenAndUrl = socialMediaService()->generateTokenAndUrl($user);
                $connectionUrl = $tokenAndUrl['url'] ?? null;
            }

            $platformErrors = [];
            if (property_exists($e, 'platformErrors')) {
                /** @var array $exceptionPlatformErrors */
                $exceptionPlatformErrors = $e->platformErrors ?? [];
                if (is_array($exceptionPlatformErrors)) {
                    $platformErrors = $exceptionPlatformErrors;
                }
            }
            $affectedPlatforms = array_column($platformErrors, 'platform');

            return response()->json([
                'error' => 'Social media platforms not linked',
                'error_code' => 'PLATFORMS_NOT_LINKED',
                'message' => 'Some social media platforms are not connected to your account.',
                'action_required' => 'LINK_PLATFORMS',
                'connection_url' => $connectionUrl,
                'platforms_affected' => $affectedPlatforms,
                'platform_errors' => $platformErrors,
                'instructions' => 'Click the connection URL to link the missing social media platforms to your account.',
            ], 403);

        } catch (\Exception $ex) {
            return response()->json([
                'error' => 'Social media platforms not linked',
                'error_code' => 'PLATFORMS_NOT_LINKED',
                'message' => 'Some social media platforms are not connected to your account.',
                'action_required' => 'LINK_PLATFORMS',
                'connection_url' => null,
                'instructions' => 'Please contact support to link your social media platforms.',
            ], 403);
        }
    }

    public function storeMediaFromUrl(StoreMediaFromUrlRequest $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $isPrivate = $request->boolean('is_private', false);
            $name = $request->input('name');

            $media = mediaService()->storeFromUrl(
                $request->input('url'),
                $user,
                null,
                $isPrivate,
                $name
            );

            return response()->json([
                'data' => new MediaResource($media),
                'success' => true,
                'message' => 'Media stored from URL successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while storing media from URL',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getMediaUrl(GetMediaUrlRequest $request, string $uuid): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $media = mediaService()->getByUuid($uuid);

            if (! $media || $media->user_id !== $user->id) {
                return response()->json(['error' => 'Media not found'], 404);
            }

            $expirationMinutes = $request->input('expiration_minutes');
            $url = mediaService()->getUrl($media, $expirationMinutes);

            return response()->json([
                'data' => [
                    'url' => $url,
                    'expires_at' => $expirationMinutes ? now()->addMinutes($expirationMinutes) : null,
                ],
                'success' => true,
                'message' => 'Media URL generated successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'An error occurred while generating media URL',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function getPosts(GetPostsRequest $request): JsonResponse
    {
        $user = $request->user();

        if (! $user) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        try {
            $hasConnectedAccount = $this->checkUserSocialConnection($user);
            if (! $hasConnectedAccount) {
                return $this->handleSocialConnectionRequired($user);
            }

            $filters = array_filter([
                'platform' => $request->input('platform'),
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
            ]);

            $perPage = $request->input('per_page', 20);
            $page = $request->input('page', 1);

            $posts = socialMediaService()->getPosts($user, $filters, $perPage, $page);

            if (! $posts) {
                return response()->json([
                    'error' => 'Failed to retrieve posts',
                    'message' => 'Unable to fetch posts from social media platform.',
                ], 500);
            }

            return response()->json([
                'data' => $posts['posts'] ?? [],
                'meta' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $posts['total'] ?? 0,
                    'platform' => $request->input('platform', 'all'),
                ],
                'success' => true,
                'message' => 'Posts retrieved successfully',
            ]);

        } catch (\Exception $e) {
            if ($this->isSocialConnectionError($e)) {
                return $this->handleSocialConnectionRequired($user);
            }

            return response()->json([
                'error' => 'An error occurred while retrieving posts',
                'details' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}
