<?php

namespace App\Http\Controllers;

use App\Models\Campaign;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;

class BuildController extends Controller
{
    public function index()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        return Inertia::render('Build/BuildIndex', []);
    }

    public function posts()
    {
        // Redirect to the platform selection page
        return redirect()->route('build.posts.platforms');
    }

    public function postsQuestions()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        return Inertia::render('Build/PostsQuestions', []);
    }

    public function savePostsQuestions(Request $request)
    {
        $request->validate([
            'business_description' => 'required|string|max:1000',
            'target_audience' => 'required|string|max:500',
            'post_tone' => 'required|string|in:professional,casual,friendly,authoritative,playful',
            'key_messages' => 'nullable|string|max:1000',
            'call_to_action' => 'nullable|string|max:200',
        ]);

        // Store the data in session to pass to the posts page
        session([
            'posts_questions_data' => $request->only([
                'business_description',
                'target_audience',
                'post_tone',
                'key_messages',
                'call_to_action'
            ])
        ]);

        return redirect()->route('build.posts.sources');
    }

    public function postsSources()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        return Inertia::render('Build/PostsSources', []);
    }

    public function savePostsSources(Request $request)
    {
        $request->validate([
            'platforms' => 'required|array',
            'platforms.*' => 'integer|min:1|max:5',
            'source' => 'nullable|array',
            'source.type' => 'nullable|string|in:text,url,pdf',
            'source.content' => 'nullable|string',
            'source.fileName' => 'nullable|string'
        ]);

        $platforms = $request->input('platforms');
        $source = $request->input('source', []);

        // Store platform selection and source data in session
        session([
            'posts_platforms_data' => $platforms,
            'posts_source_data' => $source
        ]);

        return redirect()->route('build.posts.content');
    }

    public function postsContent()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        // Get the data from session
        $platformsData = session('posts_platforms_data', []);
        $sourceData = session('posts_source_data', []);

        return Inertia::render('Build/PostsContent', [
            'platformsData' => $platformsData,
            'sourceData' => $sourceData,
            'social_media_channels' => config('affinity.social_media_channels'),
            'social_url' => socialMediaService()->getSocialUrl(socialMediaService()->generateToken(auth()->user())),
        ]);
    }

    public function calendar()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        return Inertia::render('Build/Calendar', []);
    }

    public function campaign()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        return Inertia::render('Build/Campaign', []);
    }

    public function saveCampaign(Request $request)
    {
        $request->validate([
            'campaign_type' => 'required|string',
            'target_amount' => 'nullable|numeric',
            'campaign_description' => 'nullable|string|max:1000'
        ]);

        // Store campaign data in session for use in subsequent steps
        session([
            'campaign_data' => $request->only([
                'campaign_type',
                'target_amount',
                'campaign_description'
            ])
        ]);

        return redirect()->route('build.audience');
    }

    public function audience()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        $user = auth()->user();
        $company = $user->getCompany();
        $general_category = $company ? $company->general_category : null;
        $sub_category = $company ? $company->sub_category : null;

        return Inertia::render('Build/Audience', [
            'general_category' => $general_category,
            'sub_category' => $sub_category,
            'social_media_channels' => config('affinity.social_media_channels'),
            'communication_channels' => config('affinity.communication_channels'),
            'fundraising_campaigns' => config('affinity.fundraising_campaigns'),
        ]);
    }

    public function audience_result(Request $request, $uuid = null)
    {
        if (auth()->check()) {
            auth()->user()->refresh();
        }

        $user = auth()->user();
        $company = $user->getCompany();
        $general_category = $company ? $company->general_category : null;
        $sub_category = $company ? $company->sub_category : null;

        $campaign = null;

        if ($uuid) {
            $campaign = Campaign::where('user_id', $user->id)
                ->where('uuid', $uuid)
                ->firstOrFail();
        }

        return Inertia::render('Build/AudienceResult', [
            'campaign' => $campaign,
            'general_category' => $general_category,
            'sub_category' => $sub_category,
            'social_media_channels' => config('affinity.social_media_channels'),
            'communication_channels' => config('affinity.communication_channels'),
            'fundraising_campaigns' => config('affinity.fundraising_campaigns'),
        ]);
    }

    public function campaignPlatforms()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        // Get audience data from session or store
        $audienceData = session('campaign_audience_data', []);

        return Inertia::render('Build/CampaignPlatforms', [
            'social_media_channels' => config('affinity.social_media_channels'),
            'audienceData' => $audienceData
        ]);
    }

    public function saveCampaignPlatforms(Request $request)
    {
        $request->validate([
            'platforms' => 'required|array',
            'platforms.*' => 'integer|min:1|max:5',
            'audienceData' => 'nullable|array'
        ]);

        $platforms = $request->input('platforms');
        $audienceData = $request->input('audienceData', []);

        // Store platform selection in session
        session([
            'campaign_platforms_data' => $platforms,
            'campaign_audience_data' => $audienceData
        ]);

        return redirect()->route('build.campaigns.content');
    }

    public function content()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }

        // Get campaign platform data from session
        $campaignPlatformsData = session('campaign_platforms_data', []);

        return Inertia::render('Build/Content', [
            'social_media_channels' => config('affinity.social_media_channels'),
            'social_url' => socialMediaService()->getSocialUrl(socialMediaService()->generateToken(auth()->user())),
            'campaignPlatformsData' => $campaignPlatformsData
        ]);
    }

    public function schedule()
    {
        if (Auth::check()) {
            Auth::user()->refresh();
        }
        return Inertia::render('Scheduler/Schedule');
    }

    public function saveContent(Request $request)
    {
        $request->validate([
            'content' => 'required|string',
            'platforms' => 'nullable|array',
            'campaignData' => 'nullable|array',
            'audienceData' => 'nullable|array'
        ]);

        $content = $request->input('content');
        $platforms = $request->input('platforms', []);
        $campaignData = $request->input('campaignData', []);
        $audienceData = $request->input('audienceData', []);

        // TODO: Implement actual campaign content generation logic
        // This could involve:
        // 1. Processing the generated content
        // 2. Saving the generated campaign content to database
        // 3. Scheduling campaign content if needed

        $generatedContent = [];
        foreach ($platforms as $platformId => $count) {
            for ($i = 0; $i < $count; $i++) {
                $generatedContent[] = [
                    'platform' => $platformId,
                    'content' => $content,
                    'created_at' => now(),
                    'campaign_data' => $campaignData,
                    'audience_data' => $audienceData
                ];
            }
        }

        // Clear session data after use
        session()->forget(['campaign_platforms_data', 'campaign_audience_data', 'campaign_data']);

        return back()->with([
            'success' => 'Campaign content generated successfully!',
            'generated_content' => $generatedContent
        ]);
    }

    public function savePostsContent(Request $request)
    {
        $request->validate([
            'content' => 'required|string',
            'platforms' => 'nullable|array',
            'questionsData' => 'nullable|array'
        ]);

        $content = $request->input('content');
        $platforms = $request->input('platforms', []);
        $questionsData = $request->input('questionsData', []);

        // TODO: Implement actual post generation logic
        // This could involve:
        // 1. Processing the generated content
        // 2. Saving the generated posts to database
        // 3. Scheduling posts if needed

        $generatedPosts = [];
        foreach ($platforms as $platformId => $count) {
            for ($i = 0; $i < $count; $i++) {
                $generatedPosts[] = [
                    'platform' => $platformId,
                    'content' => $content,
                    'created_at' => now(),
                    'questions_data' => $questionsData
                ];
            }
        }

        // Clear session data after use
        session()->forget(['posts_questions_data', 'posts_platforms_data']);

        return back()->with([
            'success' => 'Posts generated successfully!',
            'generated_posts' => $generatedPosts
        ]);
    }

    public function saveCalendar(Request $request)
    {
        // TODO: Implement calendar saving logic
        return response()->json(['message' => 'Calendar saved successfully']);
    }
}
