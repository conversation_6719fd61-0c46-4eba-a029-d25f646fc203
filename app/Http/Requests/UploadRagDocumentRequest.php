<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadRagDocumentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'document' => [
                'required',
                'file',
                'mimes:pdf,txt,csv,md,docx',
                'max:10240', // 10MB
            ],
            'collection_id' => [
                'required',
                'numeric',
                'exists:user_rag_accounts,id',
            ],
            'title' => [
                'sometimes',
                'nullable',
                'string',
                'max:255',
            ],
            'metadata' => [
                'sometimes',
                'array',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'document.required' => 'Please select a document to upload.',
            'document.file' => 'The selected file is invalid.',
            'document.mimes' => 'The document must be a PDF, TXT, CSV, Markdown, or Word file.',
            'document.max' => 'The document must not exceed 10MB.',
            'collection_id.required' => 'Please select a collection to upload to.',
            'collection_id.exists' => 'The selected RAG collection is invalid.',
            'title.max' => 'The document title is too long.',
        ];
    }
}
