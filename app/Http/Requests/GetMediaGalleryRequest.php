<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetMediaGalleryRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'mime_type' => 'nullable|string|in:image,video',
            'is_private' => 'nullable|boolean',
            'search' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'page.integer' => 'Page must be a valid integer.',
            'page.min' => 'Page must be at least 1.',
            'per_page.integer' => 'Per page must be a valid integer.',
            'per_page.min' => 'Per page must be at least 1.',
            'per_page.max' => 'Per page must not exceed 100.',
            'mime_type.in' => 'Mime type must be either image or video.',
            'search.max' => 'Search term must not exceed 255 characters.',
        ];
    }
}
