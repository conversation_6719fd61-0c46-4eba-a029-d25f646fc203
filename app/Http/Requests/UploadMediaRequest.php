<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadMediaRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'file' => [
                'required',
                'file',
                'max:'.config('media.max_file_size', 51200),
                'mimetypes:'.implode(',', config('media.allowed_mime_types', [])),
            ],
            'is_private' => 'nullable|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'file.required' => 'A file is required for upload.',
            'file.file' => 'The uploaded item must be a valid file.',
            'file.max' => 'The file size must not exceed :max KB.',
            'file.mimetypes' => 'The file type is not supported.',
        ];
    }
}
