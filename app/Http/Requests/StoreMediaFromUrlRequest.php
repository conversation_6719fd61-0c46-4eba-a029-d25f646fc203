<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreMediaFromUrlRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'url' => 'required|url',
            'name' => 'nullable|string|max:255',
            'is_private' => 'nullable|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'url.required' => 'A URL is required.',
            'url.url' => 'The URL must be a valid URL.',
            'name.max' => 'The name must not exceed 255 characters.',
        ];
    }
}
