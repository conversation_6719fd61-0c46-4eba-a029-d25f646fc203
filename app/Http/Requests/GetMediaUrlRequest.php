<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetMediaUrlRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'expiration_minutes' => 'nullable|integer|min:1|max:10080',
        ];
    }

    public function messages(): array
    {
        return [
            'expiration_minutes.integer' => 'Expiration minutes must be a valid integer.',
            'expiration_minutes.min' => 'Expiration minutes must be at least 1.',
            'expiration_minutes.max' => 'Expiration minutes must not exceed 10080 (1 week).',
        ];
    }
}
