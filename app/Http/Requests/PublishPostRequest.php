<?php

namespace App\Http\Requests;

use App\Enums\SocialPlatform;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class PublishPostRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'post' => 'required|string|max:5000',
            'platforms' => 'required|array|min:1',
            'platforms.*' => [new Enum(SocialPlatform::class)],
            'mediaUrls' => 'nullable|array|max:10',
            'mediaUrls.*' => 'url',
            'scheduleDate' => 'nullable|date|after:now',
            'profileKey' => 'nullable|string',
            'youTubeOptions' => 'nullable|array',
            'youTubeOptions.title' => 'nullable|string|max:100',
            'youTubeOptions.description' => 'nullable|string|max:5000',
            'youTubeOptions.tags' => 'nullable|array',
            'youTubeOptions.categoryId' => 'nullable|string',
            'youTubeOptions.privacyStatus' => 'nullable|string|in:private,public,unlisted',
            'linkedInOptions' => 'nullable|array',
            'linkedInOptions.title' => 'nullable|string|max:200',
            'linkedInOptions.description' => 'nullable|string|max:1300',
            'tiktokOptions' => 'nullable|array',
            'tiktokOptions.privacyLevel' => 'nullable|string|in:SELF_ONLY,MUTUAL_FOLLOW_FRIENDS,FOLLOWER_OF_CREATOR,PUBLIC_TO_EVERYONE',
            'tiktokOptions.brandContentToggle' => 'nullable|boolean',
            'tiktokOptions.brandOrganicToggle' => 'nullable|boolean',
            'shortenLinks' => 'nullable|boolean',
            'muteVideo' => 'nullable|boolean',
            'autoHashtag' => 'nullable|array',
            'autoHashtag.position' => 'nullable|string|in:auto,beginning,end',
            'autoHashtag.number' => 'nullable|integer|min:1|max:30',
            'boost' => 'nullable|array',
            'boost.facebook' => 'nullable|array',
            'boost.instagram' => 'nullable|array',
            'replyTo' => 'nullable|string',
            'lat' => 'nullable|numeric|between:-90,90',
            'long' => 'nullable|numeric|between:-180,180',
            'altText' => 'nullable|string|max:1000',
            'priority' => 'nullable|string|in:high,normal',
            'poll' => 'nullable|array',
            'poll.options' => 'nullable|array|min:2|max:4',
            'poll.durationMinutes' => 'nullable|integer|min:5|max:10080',
            'threadOptions' => 'nullable|array',
            'threadOptions.altText' => 'nullable|string|max:1000',
            'threadOptions.title' => 'nullable|string|max:500',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'post.required' => 'Post content is required.',
            'post.max' => 'Post content cannot exceed 5000 characters.',
            'platforms.required' => 'At least one platform must be selected.',
            'platforms.min' => 'At least one platform must be selected.',
            'platforms.*.enum' => 'Invalid platform selected.',
            'mediaUrls.max' => 'Maximum 10 media URLs are allowed.',
            'mediaUrls.*.url' => 'Each media URL must be a valid URL.',
            'scheduleDate.date' => 'Schedule date must be a valid date.',
            'scheduleDate.after' => 'Schedule date must be in the future.',
            'youTubeOptions.title.max' => 'YouTube title cannot exceed 100 characters.',
            'youTubeOptions.description.max' => 'YouTube description cannot exceed 5000 characters.',
            'youTubeOptions.privacyStatus.in' => 'YouTube privacy status must be private, public, or unlisted.',
            'linkedInOptions.title.max' => 'LinkedIn title cannot exceed 200 characters.',
            'linkedInOptions.description.max' => 'LinkedIn description cannot exceed 1300 characters.',
            'tiktokOptions.privacyLevel.in' => 'Invalid TikTok privacy level.',
            'autoHashtag.position.in' => 'Auto hashtag position must be auto, beginning, or end.',
            'autoHashtag.number.min' => 'Auto hashtag number must be at least 1.',
            'autoHashtag.number.max' => 'Auto hashtag number cannot exceed 30.',
            'lat.between' => 'Latitude must be between -90 and 90.',
            'long.between' => 'Longitude must be between -180 and 180.',
            'altText.max' => 'Alt text cannot exceed 1000 characters.',
            'priority.in' => 'Priority must be high or normal.',
            'poll.options.min' => 'Poll must have at least 2 options.',
            'poll.options.max' => 'Poll cannot have more than 4 options.',
            'poll.durationMinutes.min' => 'Poll duration must be at least 5 minutes.',
            'poll.durationMinutes.max' => 'Poll duration cannot exceed 10080 minutes (7 days).',
            'threadOptions.altText.max' => 'Thread alt text cannot exceed 1000 characters.',
            'threadOptions.title.max' => 'Thread title cannot exceed 500 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'post' => 'post content',
            'platforms' => 'platforms',
            'mediaUrls' => 'media URLs',
            'scheduleDate' => 'schedule date',
            'profileKey' => 'profile key',
            'youTubeOptions.title' => 'YouTube title',
            'youTubeOptions.description' => 'YouTube description',
            'youTubeOptions.tags' => 'YouTube tags',
            'youTubeOptions.categoryId' => 'YouTube category',
            'youTubeOptions.privacyStatus' => 'YouTube privacy status',
            'linkedInOptions.title' => 'LinkedIn title',
            'linkedInOptions.description' => 'LinkedIn description',
            'tiktokOptions.privacyLevel' => 'TikTok privacy level',
            'tiktokOptions.brandContentToggle' => 'TikTok brand content toggle',
            'tiktokOptions.brandOrganicToggle' => 'TikTok brand organic toggle',
            'shortenLinks' => 'shorten links',
            'muteVideo' => 'mute video',
            'autoHashtag.position' => 'auto hashtag position',
            'autoHashtag.number' => 'auto hashtag number',
            'boost.facebook' => 'Facebook boost',
            'boost.instagram' => 'Instagram boost',
            'replyTo' => 'reply to',
            'lat' => 'latitude',
            'long' => 'longitude',
            'altText' => 'alt text',
            'priority' => 'priority',
            'poll.options' => 'poll options',
            'poll.durationMinutes' => 'poll duration',
            'threadOptions.altText' => 'thread alt text',
            'threadOptions.title' => 'thread title',
        ];
    }
}
