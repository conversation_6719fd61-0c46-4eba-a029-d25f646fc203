<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MediaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'name' => $this->name,
            'mime' => $this->mime,
            'size' => $this->size,
            'size_mb' => $this->getSizeInMB(),
            'is_private' => $this->is_private,
            'is_image' => $this->isImage(),
            'is_video' => $this->isVideo(),
            'url' => $this->external_url ?: $this->url,
            'exists' => $this->external_url ? true : $this->exists(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'width' => $this->when($this->isImage() && ! $this->external_url, function () {
                return $this->getImageDimensions()['width'] ?? null;
            }),
            'height' => $this->when($this->isImage() && ! $this->external_url, function () {
                return $this->getImageDimensions()['height'] ?? null;
            }),
        ];
    }

    /**
     * Get image dimensions if available
     */
    private function getImageDimensions(): array
    {
        try {
            if ($this->external_url || ! $this->exists()) {
                return [];
            }

            $fullPath = $this->full_path;
            if (function_exists('getimagesize')) {
                $imageInfo = getimagesize($fullPath);
                if ($imageInfo) {
                    return [
                        'width' => $imageInfo[0],
                        'height' => $imageInfo[1],
                    ];
                }
            }
        } catch (\Exception $e) {
            // Ignore image metadata errors
        }

        return [];
    }
}
