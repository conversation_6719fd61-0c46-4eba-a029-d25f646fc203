<?php

namespace App\Services\V1\AIService;

use App\Contracts\AIDriver\AIDriverInterface;
use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use Closure;
use Exception;

class AssistantService
{
    protected AIServiceFactory $factory;

    protected ?AIDriverInterface $driver = null;

    protected ?string $currentProvider = null;

    public function __construct(AIServiceFactory $factory)
    {
        $this->factory = $factory;
    }

    /**
     * Set the AI provider to use
     */
    public function useProvider(string $provider): self
    {
        $this->driver = $this->factory->make($provider);
        $this->currentProvider = $provider;

        return $this;
    }

    /**
     * Get the current driver (creates default if none set)
     */
    protected function getDriver(): AIDriverInterface
    {
        if (! $this->driver) {
            $this->driver = $this->factory->make();
            $this->currentProvider = $this->factory->getDefaultProvider();
        }

        return $this->driver;
    }

    /**
     * Prepare messages for AI based on request data and company info
     * Supports both initial prompts and continuous chat
     */
    public function prepareMessages(array $validated, $company, ?string $systemPrompt = null): array
    {
        if (isset($validated['messages']) && is_array($validated['messages']) && ! empty($validated['messages'])) {
            $messages = $validated['messages'];

            if (empty($messages) || $messages[0]['role'] !== 'system') {
                $systemPrompt = $systemPrompt ?? config('prompts')['system'] ?? 'You are a helpful assistant.';
                array_unshift($messages, ['role' => 'system', 'content' => $systemPrompt]);
            }
        } else {
            $systemPrompt = $systemPrompt ?? $this->getSystemPrompt($validated, $company);

            $messages = [
                ['role' => 'system', 'content' => $systemPrompt],
            ];

            if (! empty($validated['feature'])) {
                $templatePrompt = $this->generateTemplatePrompt($validated['feature'], $company, $validated);
                $messages[] = ['role' => 'user', 'content' => $templatePrompt];
            }
        }

        return $this->getDriver()->prepareMessages($messages);
    }

    private function getSystemPrompt(array $validated, $company): string
    {
        if (! empty($validated['feature'])) {
            $databasePrompt = $this->getDatabasePrompt($validated);
            if ($databasePrompt) {
                return $this->replacePlaceholders($databasePrompt, $company, $validated);
            }
        }

        return config('prompts')['system'] ?? 'You are a helpful assistant.';
    }

    private function getDatabasePrompt(array $validated): ?string
    {
        $feature = $validated['feature'] ?? '';

        $promptType = $this->determinePromptType($feature, $validated);
        $platform = $this->determinePlatform($validated);

        return getPromptText($promptType, $platform);
    }

    private function determinePromptType(string $feature, array $validated): PromptType
    {
        $campaignType = $validated['campaign_type'] ?? '';

        if (str_contains(strtolower($campaignType), 'campaign') ||
            in_array($feature, ['awareness', 'campaigns', 'stewardship', 'donations', 'lead_capture'])) {
            return PromptType::CAMPAIGN;
        }

        return PromptType::QUICK_POST;
    }

    private function determinePlatform(array $validated): SocialPlatform
    {
        $platform = $validated['selected_social_platform'] ?? $validated['platform'] ?? '';

        if ($platform && in_array($platform, SocialPlatform::values())) {
            return SocialPlatform::from($platform);
        }

        return SocialPlatform::FACEBOOK;
    }

    private function replacePlaceholders(string $promptText, $company, array $validated): string
    {
        $spellingVariant = $validated['spelling_variant'] ?? null;
        if (! $spellingVariant) {
            $region = $company->region ?? '';
            $usStates = config('regions.us_states', []);
            $spellingVariant = in_array($region, $usStates) ? 'American English' : 'British English';
        }

        // Handle post count and generate instructions
        $postCount = $validated['post_count'] ?? 5;
        $postCountInstructions = $this->generatePostCountInstructions($postCount);

        $replacements = [
            '{OrganisationName}' => $company->name ?? 'Organization',
            '{OrganisationRegion}' => $company->region ?? 'Global',
            '{Mission}' => $company->about ?? 'Our mission',
            '{PreferredLanguage}' => $company->language_preference ?? 'English',
            '{ToneOfVoice}' => $company->tone_preference ?? 'Professional',
            '{AudienceGender}' => $validated['audience_gender'] ?? 'Mixed',
            '{AudienceAge}' => $validated['audience_age'] ?? 'All ages',
            '{AudienceIncome}' => $validated['audience_income'] ?? 'All income levels',
            '{SelectedSocialMediaPlatform}' => $validated['selected_social_platform'] ?? '',
            '{SelectedCommunicationChannel}' => $validated['selected_communication_channel'] ?? '',
            '{FundraisingCampaigns}' => $validated['fundraising_campaigns'] ?? '',
            '{PreferredCommunicationChannel}' => $validated['preferred_communication_channel'] ?? '',
            '{LikelyDonationAmount}' => $validated['likely_donation_amount'] ?? '',
            '{SpellingVariant}' => $spellingVariant,
            '{CampaignType}' => $validated['campaign_type'] ?? '',
            '{CampaignStartDate}' => $validated['campaign_start_date'] ?? '',
            '{CampaignEndDate}' => $validated['campaign_end_date'] ?? '',
            '{FundraisingTarget}' => $validated['fundraising_target'] ?? '',
            '{CallToAction}' => $validated['call_to_action'] ?? '',
            '{PostCount}' => $postCount,
            '{PostCountInstructions}' => $postCountInstructions,
        ];

        return strtr($promptText, $replacements);
    }

    /**
     * Generate template prompt based on feature and company data
     */
    protected function generateTemplatePrompt(string $feature, $company, array $validated): string
    {
        $databasePrompt = $this->getDatabasePrompt($validated);

        if ($databasePrompt) {
            return $this->replacePlaceholders($databasePrompt, $company, $validated);
        }

        $systemPrompts = config('prompts');
        $promptTemplate = $systemPrompts[$feature] ?? $systemPrompts['default'] ?? 'Please help with the following request.';

        return $this->replacePlaceholders($promptTemplate, $company, $validated);
    }

    /**
     * Generate post count specific instructions
     */
    protected function generatePostCountInstructions(int $postCount): string
    {
        if ($postCount === 1) {
            return 'Create one high-quality post that captures the essence of the message.';
        } elseif ($postCount <= 3) {
            return "Vary archetype across posts: {Story | Impact Stat | CTA}. Each post should have a distinct angle.";
        } elseif ($postCount <= 5) {
            return "Vary archetype sequentially: {Story | Impact Stat | Behind-Scenes | Quote | CTA Blast}. Each post should have a distinct angle and approach.";
        } else {
            return "Create diverse content types including: stories, impact statistics, behind-the-scenes content, quotes, testimonials, and call-to-action posts. Ensure each post has a unique angle and approach to maximize engagement.";
        }
    }

    /**
     * Stream response from AI provider
     */
    public function streamResponse(array $messages, Closure $callback): void
    {
        $driver = $this->getDriver();

        if (! $driver->supportsStreaming()) {
            throw new Exception("Current provider '{$this->currentProvider}' does not support streaming.");
        }

        $driver->streamResponse($messages, $callback);
    }

    /**
     * Get non-streaming response from AI provider (fallback)
     */
    public function getNonStreamResponse(array $messages): array
    {
        return $this->getDriver()->getNonStreamResponse($messages);
    }

    /**
     * Get the current provider name
     */
    public function getCurrentProvider(): string
    {
        return $this->currentProvider ?? $this->factory->getDefaultProvider();
    }

    /**
     * Check if current driver supports streaming
     */
    public function supportsStreaming(): bool
    {
        return $this->getDriver()->supportsStreaming();
    }

    /**
     * Get available providers
     */
    public function getAvailableProviders(): array
    {
        return $this->factory->getAvailableProviders();
    }

    /**
     * Switch provider dynamically
     */
    public function switchProvider(string $provider): self
    {
        if (! $this->factory->hasProvider($provider)) {
            throw new Exception("Provider '{$provider}' is not available.");
        }

        return $this->useProvider($provider);
    }

    /**
     * Get provider information
     */
    public function getProviderInfo(): array
    {
        $driver = $this->getDriver();

        return [
            'name' => $driver->getProviderName(),
            'supports_streaming' => $driver->supportsStreaming(),
            'current_provider' => $this->getCurrentProvider(),
            'available_providers' => $this->getAvailableProviders(),
        ];
    }

    /**
     * Reset to default provider
     */
    public function resetToDefault(): self
    {
        $this->driver = null;
        $this->currentProvider = null;

        return $this;
    }
}
