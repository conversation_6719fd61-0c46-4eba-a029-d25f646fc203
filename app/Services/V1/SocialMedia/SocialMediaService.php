<?php

namespace App\Services\V1\SocialMedia;

use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\SocialMedia\SocialMediaAdapterInterface;
use App\Enums\SocialPlatform;
use App\Enums\SocialPlatformType;
use App\Events\CreateSocialMediaAccountEvent;
use App\Models\SocialMediaPost;
use App\Models\User;
use App\Models\UserSocialAccount;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class SocialMediaService implements SocialMediaServiceInterface
{
    /**
     * Collection of adapters for different platforms
     */
    protected array $adapters = [];

    /**
     * Default platform to use when none specified
     */
    protected ?string $defaultPlatform = null;

    /**
     * Create a new Social Media service instance.
     */
    public function __construct()
    {
        $this->defaultPlatform = Config::get('socialmedia.default');
    }

    /**
     * Register a social media adapter for a platform
     */
    public function registerAdapter(string $platform, SocialMediaAdapterInterface $adapter): self
    {
        $this->adapters[$platform] = $adapter;

        return $this;
    }

    /**
     * Get the adapter for a specific platform
     */
    public function getAdapter(?string $platform = null): ?SocialMediaAdapterInterface
    {
        $platform = $platform ?? $this->defaultPlatform;

        if (! $platform || ! isset($this->adapters[$platform])) {
            Log::error('Social media adapter not found', [
                'platform' => $platform ?? 'null',
                'available_adapters' => array_keys($this->adapters),
            ]);

            return null;
        }

        return $this->adapters[$platform];
    }

    /**
     * Generate a token for a user.
     * Supports both User object with platform, and direct userId string.
     *
     * @param  User|string  $userOrUserId  Either a User object or a platform user ID string
     * @param  string|null  $platform  Optional platform identifier (only used when first param is User)
     * @return string|null Token or null on failure
     */
    public function generateToken(User|string $userOrUserId, ?string $platform = null): ?string
    {
        if (is_string($userOrUserId)) {
            return $this->generateTokenByUserId($userOrUserId);
        }

        $user = $userOrUserId;

        try {
            $platform = $platform ?? $this->defaultPlatform;
            $adapter = $this->getAdapter($platform);

            if (! $adapter) {
                return null;
            }

            $userSocialAccount = $user->socialAccounts()
                ->where('platform', $platform)
                ->where('is_active', true)
                ->first();

            if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
                Log::error("Cannot generate token - no {$platform} integration found", [
                    'user_id' => $user->id,
                    'platform' => $platform,
                ]);

                Event::dispatch(new CreateSocialMediaAccountEvent($user, $platform));

                return null;
            }

            $token = $adapter->generateToken($userSocialAccount->platform_user_id);

            if ($token) {
                $userSocialAccount->access_token = $token;
                $userSocialAccount->token_expires_at = $this->getTokenExpirationTime($platform);
                $userSocialAccount->save();

                return $token;
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error generating social media token', [
                'userId' => $user->id,
                'platform' => $platform,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Implementation for generating token with a userId string
     */
    protected function generateTokenByUserId(string $userId): ?string
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            return $adapter->generateToken($userId);
        } catch (\Exception $e) {
            Log::error('Error generating social media token', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Get token expiration time for a platform
     *
     * @param  string  $platform  The platform identifier
     * @return \Carbon\Carbon The expiration timestamp
     */
    protected function getTokenExpirationTime(string $platform): \Carbon\Carbon
    {
        $expirationMinutes = Config::get("socialmedia.token_expiration.{$platform}")
            ?? Config::get('socialmedia.token_expiration.default', 720);

        return now()->addMinutes($expirationMinutes);
    }

    /**
     * {@inheritDoc}
     */
    public function createUserSocialAccount(
        User $user,
        SocialPlatformType $platform,
        array $accountData
    ): ?UserSocialAccount {
        try {
            $adapter = $this->getAdapter($platform->value);

            if (! $adapter) {
                throw new \Exception("No adapter found for platform: {$platform->value}");
            }

            $userData = $adapter->createUser([
                'email' => $user->email,
                'firstName' => $user->first_name,
                'lastName' => $user->last_name,
            ]);

            if (isset($userData['user_id'])) {
                $accountData['platform_user_id'] = $userData['user_id'];

                $userSocialAccounts = userSocialAccountService()->linkAccount($user, $platform->value, $accountData);
            }

            return $userSocialAccounts;
        } catch (\Exception $e) {
            Log::error('Failed to create/update social media account', [
                'user_id' => $user->id,
                'platform' => $platform->value,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * {@inheritDoc}
     */
    public function createUser(array $userData): ?array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            return $adapter->createUser($userData);
        } catch (\Exception $e) {
            Log::error('Error creating social media user', [
                'error' => $e->getMessage(),
                'userData' => $userData,
            ]);

            return null;
        }
    }

    /**
     * {@inheritDoc}
     */
    public function publishPost(array $postData, User $user): ?SocialMediaPost
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            if (isset($postData['platforms'])) {
                foreach ($postData['platforms'] as $platform) {
                    if (! SocialPlatform::tryFrom($platform)) {
                        Log::error('Invalid platform specified', [
                            'platform' => $platform,
                            'allowedPlatforms' => SocialPlatform::values(),
                        ]);
                    }
                }
            }

            $userSocialAccount = $user->socialAccounts()
                ->where('platform', $this->defaultPlatform)
                ->where('is_active', true)
                ->first();

            if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
                Log::error("Cannot publish post - no {$this->defaultPlatform} integration found", [
                    'user_id' => $user->id,
                    'platform' => $this->defaultPlatform,
                ]);

                Event::dispatch(new CreateSocialMediaAccountEvent($user, $this->defaultPlatform));

                return null;
            }

            $adapterData = $this->prepareAdapterData($postData, $userSocialAccount->platform_user_id);
            $result = $adapter->publishPost($adapterData);

            if (! $result['success']) {
                Log::error('Adapter returned failure', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'post_data' => $postData,
                ]);

                return null;
            }

            $post = new SocialMediaPost([
                'user_id' => $user->id,
                'content' => $postData['content'],
                'platforms' => $postData['platforms'],
                'media_urls' => $postData['media_urls'] ?? [],
                'status' => $result['status'] ?? (! empty($postData['scheduled_at']) ? 'scheduled' : 'published'),
                'scheduled_at' => $postData['scheduled_at'] ?? null,
                'post_group_id' => $result['post_id'],
                'post_data' => $result,
            ]);

            $post->save();

            return $post;
        } catch (\Exception $e) {
            $connectionErrors = [
                'Platform linkage errors detected',
                'Unauthorized:',
                'Forbidden:',
                'Profile not found:',
            ];

            foreach ($connectionErrors as $errorPattern) {
                if (strpos($e->getMessage(), $errorPattern) === 0) {
                    throw $e;
                }
            }

            Log::error('Error publishing social media post', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    private function prepareAdapterData(array $postData, string $profileKey): array
    {
        $adapterData = [
            'post' => $postData['content'],
            'platforms' => $postData['platforms'],
            'profileKey' => $profileKey,
        ];

        if (! empty($postData['scheduled_at'])) {
            $scheduledDate = new \DateTime($postData['scheduled_at']);
            $adapterData['scheduleDate'] = $scheduledDate->format('Y-m-d\TH:i:s\Z');
        }

        $directMappings = [
            'media_urls' => 'mediaUrls',
            'youTubeOptions' => 'youTubeOptions',
            'linkedInOptions' => 'linkedInOptions',
            'tiktokOptions' => 'tiktokOptions',
            'shortenLinks' => 'shortenLinks',
            'muteVideo' => 'muteVideo',
            'autoHashtag' => 'autoHashtag',
            'boost' => 'boost',
            'replyTo' => 'replyTo',
            'lat' => 'lat',
            'long' => 'long',
            'altText' => 'altText',
            'priority' => 'priority',
            'poll' => 'poll',
            'threadOptions' => 'threadOptions',
        ];

        foreach ($directMappings as $source => $target) {
            if (isset($postData[$source]) && ! empty($postData[$source])) {
                $adapterData[$target] = $postData[$source];
            }
        }

        return $adapterData;
    }

    /**
     * {@inheritDoc}
     */
    public function getPosts(User $user, array $filters = [], int $perPage = 20, int $page = 1): array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return ['posts' => [], 'total' => 0];
            }

            $userSocialAccount = $user->socialAccounts()
                ->where('platform', $this->defaultPlatform)
                ->where('is_active', true)
                ->first();

            if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
                return ['posts' => [], 'total' => 0];
            }

            if (! method_exists($adapter, 'getPostsWithFilters')) {
                $posts = $adapter->getPosts($userSocialAccount->platform_user_id);

                return ['posts' => $posts, 'total' => count($posts)];
            }

            return $adapter->getPostsWithFilters($userSocialAccount->platform_user_id, $filters, $perPage, $page);
        } catch (\Exception $e) {
            Log::error('Error getting social media posts', [
                'userId' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return ['posts' => [], 'total' => 0];
        }
    }

    /**
     * {@inheritDoc}
     */
    public function deletePost(SocialMediaPost $post): bool
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                Log::error('No adapter found for deleting post');

                return false;
            }

            if ($post->post_group_id && method_exists($adapter, 'deletePost')) {
                $result = $adapter->deletePost($post->post_group_id);

                if (! $result) {
                    Log::warning('Failed to delete post through social media adapter', [
                        'postId' => $post->id,
                        'post_group_id' => $post->post_group_id,
                    ]);
                }
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error deleting social media post', [
                'postId' => $post->id,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * {@inheritDoc}
     */
    public function getSocialUrl(?string $token = null): ?string
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            return $adapter->getSocialUrl($token);
        } catch (\Exception $e) {
            Log::error('Error getting social media URL', [
                'token' => $token,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function generateTokenAndUrl(User $user, ?string $platform = null): ?array
    {
        try {
            $platform = $platform ?? $this->defaultPlatform;
            $adapter = $this->getAdapter($platform);

            if (! $adapter || ! method_exists($adapter, 'generateJwtAndUrl')) {
                return null;
            }

            $userSocialAccount = $user->socialAccounts()
                ->where('platform', $platform)
                ->where('is_active', true)
                ->first();

            if (! $userSocialAccount || ! $userSocialAccount->platform_user_id) {
                Event::dispatch(new CreateSocialMediaAccountEvent($user, $platform));

                return null;
            }

            return $adapter->generateJwtAndUrl($userSocialAccount->platform_user_id);
        } catch (\Exception $e) {
            Log::error('Error generating token and URL', [
                'userId' => $user->id,
                'platform' => $platform,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function uploadMedia($file, ?string $fileName = null, ?string $description = null, ?User $user = null): ?array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            $result = $adapter->uploadMedia($file, $fileName, $description, $profileKey);

            if (! $result['success']) {
                Log::error('Media upload failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'fileName' => $fileName,
                ]);

                return null;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error uploading media', [
                'fileName' => $fileName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function getUploadUrl(string $fileName, ?string $contentType = null, ?User $user = null): ?array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            $result = $adapter->getUploadUrl($fileName, $contentType, $profileKey);

            if (! $result['success']) {
                Log::error('Upload URL generation failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'fileName' => $fileName,
                ]);

                return null;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error getting upload URL', [
                'fileName' => $fileName,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function getMediaGallery(?User $user = null): array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return [];
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            $result = $adapter->getMediaGallery($profileKey);

            if (! $result['success']) {
                Log::error('Media gallery retrieval failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                ]);

                return [];
            }

            return $result['media'] ?? [];
        } catch (\Exception $e) {
            Log::error('Error getting media gallery', [
                'error' => $e->getMessage(),
            ]);

            return [];
        }
    }

    public function resizeMedia(string $mediaId, int $width, int $height, ?User $user = null): ?array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            $result = $adapter->resizeMedia($mediaId, $width, $height, $profileKey);

            if (! $result['success']) {
                Log::error('Media resize failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'mediaId' => $mediaId,
                ]);

                return null;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error resizing media', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function getMediaMetadata(string $mediaId, ?User $user = null): ?array
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return null;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            $result = $adapter->getMediaMetadata($mediaId, $profileKey);

            if (! $result['success']) {
                Log::error('Media metadata retrieval failed', [
                    'error' => $result['error'] ?? 'Unknown error',
                    'mediaId' => $mediaId,
                ]);

                return null;
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error getting media metadata', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    public function verifyMediaUrl(string $mediaUrl, ?User $user = null): bool
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return false;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            return $adapter->verifyMediaUrl($mediaUrl, $profileKey);
        } catch (\Exception $e) {
            Log::error('Error verifying media URL', [
                'mediaUrl' => $mediaUrl,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function deleteMedia(string $mediaId, ?User $user = null): bool
    {
        try {
            $adapter = $this->getAdapter();

            if (! $adapter) {
                return false;
            }

            $profileKey = null;
            if ($user) {
                $userSocialAccount = $user->socialAccounts()
                    ->where('platform', $this->defaultPlatform)
                    ->where('is_active', true)
                    ->first();

                $profileKey = $userSocialAccount?->platform_user_id;
            }

            return $adapter->deleteMedia($mediaId, $profileKey);
        } catch (\Exception $e) {
            Log::error('Error deleting media', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
