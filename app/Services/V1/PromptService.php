<?php

namespace App\Services\V1;

use App\Contracts\Services\PromptServiceInterface;
use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Models\Prompt;
use Illuminate\Support\Collection;

class PromptService implements PromptServiceInterface
{
    public function getPrompt(PromptType $type, SocialPlatform $platform): ?Prompt
    {
        return Prompt::findByTypeAndPlatform($type, $platform);
    }

    public function getPromptText(PromptType $type, SocialPlatform $platform): ?string
    {
        $prompt = $this->getPrompt($type, $platform);

        return $prompt?->prompt_text;
    }

    public function createPrompt(array $data): Prompt
    {
        return Prompt::create($data);
    }

    public function updatePrompt(Prompt $prompt, array $data): Prompt
    {
        $prompt->update($data);

        return $prompt->fresh();
    }

    public function deletePrompt(Prompt $prompt): bool
    {
        return $prompt->delete();
    }

    public function getAllPrompts(): Collection
    {
        return Prompt::all();
    }

    public function getPromptsByType(PromptType $type): Collection
    {
        return Prompt::where('type', $type->value)->get();
    }

    public function getPromptsByPlatform(SocialPlatform $platform): Collection
    {
        return Prompt::where('platform', $platform->value)->get();
    }
}
