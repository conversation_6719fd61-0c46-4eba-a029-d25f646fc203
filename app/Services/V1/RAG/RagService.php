<?php

namespace App\Services\V1\RAG;

use App\Contracts\Services\RagServiceInterface;
use App\Enums\RagServiceProviderType;
use App\Models\RagDocument;
use App\Models\User;
use App\Models\UserRagAccount;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class RagService implements RagServiceInterface
{
    /**
     * The registered adapters.
     *
     * @var array
     */
    protected $adapters = [];

    /**
     * The default provider.
     *
     * @var string
     */
    protected $defaultProvider;

    /**
     * Create a new RAG service instance.
     */
    public function __construct()
    {
        $this->defaultProvider = Config::get('rag.default', RagServiceProviderType::ChatBees->value);
    }

    /**
     * Register a RAG service adapter
     *
     * @param  string  $provider  Provider name
     * @param  object  $adapter  Provider adapter instance
     */
    public function registerAdapter(string $provider, $adapter): self
    {
        $this->adapters[$provider] = $adapter;

        return $this;
    }

    /**
     * Get the provider adapter
     *
     * @param  string|null  $provider  Provider name
     * @return object|null The adapter or null if not found
     */
    public function getAdapter(?string $provider = null): ?object
    {
        $provider = $provider ?: $this->defaultProvider;

        return $this->adapters[$provider] ?? null;
    }

    /**
     * Create a user account on the RAG service provider
     *
     * @param  User  $user  The user to create an account for
     * @param  RagServiceProviderType  $provider  The RAG service provider
     * @param  array  $accountData  Additional account data
     * @return UserRagAccount The created user RAG account
     */
    public function createUserRagAccount(
        User $user,
        RagServiceProviderType $provider,
        array $accountData
    ): UserRagAccount {
        $adapter = $this->getAdapter($provider->value);

        if (! $adapter) {
            throw new \Exception("No adapter registered for provider: {$provider->value}");
        }

        $collectionName = $accountData['collection_name'] ?? Str::slug("{$user->id}_{$user->name}_".Str::random(8));


        $apiKey = Config::get("rag.connections.{$provider->value}.api_key");
        $accountId = Config::get("rag.connections.{$provider->value}.account_id");


        $result = $adapter->createCollection([
            'collection_name' => $collectionName,
            'namespace_name' => $accountData['namespace_name'] ?? 'public',
            'api_key' => $apiKey,
            'account_id' => $accountId,
        ]);


        return UserRagAccount::updateOrCreate(
            [
                'user_id' => $user->id,
                'provider' => $provider->value,
                'collection_name' => $collectionName,
            ],
            [
                'collection_id' => $result['collection_id'] ?? $collectionName,
                'api_key' => null, 
                'account_id' => null,
                'collection_metadata' => $result ?? [],
                'settings' => $accountData['settings'] ?? [],
                'is_active' => true,
            ]
        );
    }

    /**
     * Upload a document to the RAG service
     *
     * @param  User  $user  The user uploading the document
     * @param  string  $filePath  The path to the document
     * @param  string  $fileName  The original filename
     * @param  array  $metadata  Additional metadata
     * @param  string|null  $provider  The RAG service provider
     * @return RagDocument|null The created document or null on failure
     */
    public function uploadDocument(
        User $user,
        string $filePath,
        string $fileName,
        array $metadata = [],
        ?string $provider = null
    ): ?RagDocument {
        $provider = $provider ?: $this->defaultProvider;


        $userRagAccount = $user->ragAccounts()
            ->where('provider', $provider)
            ->where('is_active', true)
            ->first();

        if (! $userRagAccount) {
            Log::error('User has no active RAG account for provider', [
                'user_id' => $user->id,
                'provider' => $provider,
            ]);

            return null;
        }

        $adapter = $this->getAdapter($provider);

        if (! $adapter) {
            Log::error('No adapter registered for provider', [
                'provider' => $provider,
            ]);

            return null;
        }

        $storagePath = "rag/{$userRagAccount->id}/".Str::uuid().'_'.$fileName;
        $storedPath = Storage::put($storagePath, file_get_contents($filePath));
        $fileSize = Storage::size($storagePath);
        $mimeType = Storage::mimeType($storagePath);


        $document = RagDocument::create([
            'user_rag_account_id' => $userRagAccount->id,
            'original_filename' => $fileName,
            'file_path' => $storagePath,
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'status' => 'processing',
            'metadata' => $metadata,
        ]);

        $result = $adapter->uploadDocument(
            $userRagAccount->collection_id,
            Storage::path($storagePath),
            $fileName
        );


        if ($result) {
            $document->update([
                'document_id' => $result['doc_name'] ?? $fileName,
                'status' => 'completed',
            ]);


            try {
                $outlineFaq = $adapter->getDocumentOutlineFaq(
                    $userRagAccount->collection_id,
                    $document->document_id
                );

                $document->update([
                    'outlines' => $outlineFaq['outlines'] ?? [],
                    'faqs' => $outlineFaq['faqs'] ?? [],
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to get document outlines and FAQs', [
                    'error' => $e->getMessage(),
                    'document_id' => $document->id,
                ]);
            }
        } else {
            $document->update(['status' => 'failed']);
        }

        return $document;
    }

    /**
     * Ask a question using the RAG service
     *
     * @param  User  $user  The user asking the question
     * @param  string  $question  The question to ask
     * @param  string|null  $documentId  Specific document ID to query (optional)
     * @param  array  $historyMessages  Previous chat history (optional)
     * @param  string|null  $provider  The RAG service provider
     * @param  int|null  $accountId  The specific collection account ID to use
     * @return array Answer and references
     */
    public function ask(
        User $user,
        string $question,
        ?string $documentId = null,
        array $historyMessages = [],
        ?string $provider = null,
        ?int $accountId = null
    ): array {
        $provider = $provider ?: $this->defaultProvider;


        $query = $user->ragAccounts()
            ->where('provider', $provider)
            ->where('is_active', true);

     
        if ($accountId) {
            $query->where('id', $accountId);
        }

        $userRagAccount = $query->first();

        if (! $userRagAccount) {
            Log::error('User has no active RAG account for provider', [
                'user_id' => $user->id,
                'provider' => $provider,
                'account_id' => $accountId,
            ]);

            return [
                'answer' => 'You do not have an active collection for this service.',
                'refs' => [],
            ];
        }

        $adapter = $this->getAdapter($provider);

        if (! $adapter) {
            Log::error('No adapter registered for provider', [
                'provider' => $provider,
            ]);

            return [
                'answer' => 'This service is not available at the moment.',
                'refs' => [],
            ];
        }

 
        $apiKey = $userRagAccount->getApiKey();
        $accountIdValue = $userRagAccount->getAccountId();

        if (! $apiKey || ! $accountIdValue) {
            Log::error('Missing API credentials for provider in configuration', [
                'provider' => $provider,
            ]);

            return [
                'answer' => 'There was an issue connecting to the RAG service. Please contact your administrator.',
                'refs' => [],
            ];
        }

        if ($documentId && Str::isUuid($documentId)) {
            $document = RagDocument::where('uuid', $documentId)
                ->where('user_rag_account_id', $userRagAccount->id)
                ->first();

            if ($document) {
                $documentId = $document->document_id;
            }
        }

        try {

            $response = $adapter->ask(
                $userRagAccount->collection_id,
                $question,
                $documentId,
                $historyMessages,
                ['api_key' => $apiKey, 'account_id' => $accountIdValue]
            );

            return $response;
        } catch (\Exception $e) {
            Log::error('Error querying RAG service', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'provider' => $provider,
            ]);

            return [
                'answer' => 'There was an issue processing your request. Please try again later.',
                'refs' => [],
            ];
        }
    }

    /**
     * Get document outlines and FAQs from the RAG service
     *
     * @param  User  $user  The user
     * @param  string  $documentId  The document ID
     * @param  string|null  $provider  The RAG service provider
     * @return array Outlines and FAQs
     */
    public function getDocumentOutlineFaq(
        User $user,
        string $documentId,
        ?string $provider = null
    ): array {
        $provider = $provider ?: $this->defaultProvider;


        $userRagAccount = $user->ragAccounts()
            ->where('provider', $provider)
            ->where('is_active', true)
            ->first();

        if (! $userRagAccount) {
            Log::error('User has no active RAG account for provider', [
                'user_id' => $user->id,
                'provider' => $provider,
            ]);

            return [
                'outlines' => [],
                'faqs' => [],
            ];
        }

        $adapter = $this->getAdapter($provider);

        if (! $adapter) {
            Log::error('No adapter registered for provider', [
                'provider' => $provider,
            ]);

            return [
                'outlines' => [],
                'faqs' => [],
            ];
        }


        $apiKey = $userRagAccount->getApiKey();
        $accountIdValue = $userRagAccount->getAccountId();

        if (! $apiKey || ! $accountIdValue) {
            Log::error('Missing API credentials for provider in configuration', [
                'provider' => $provider,
            ]);

            return [
                'outlines' => [],
                'faqs' => [],
            ];
        }


        if (Str::isUuid($documentId)) {
            $document = RagDocument::where('uuid', $documentId)
                ->where('user_rag_account_id', $userRagAccount->id)
                ->first();

            if ($document) {
                if ($document->outlines || $document->faqs) {
                    return [
                        'outlines' => $document->outlines,
                        'faqs' => $document->faqs,
                    ];
                }

                $documentId = $document->document_id;
            }
        }

        try {

            $result = $adapter->getDocumentOutlineFaq(
                $userRagAccount->collection_id,
                $documentId,
                ['api_key' => $apiKey, 'account_id' => $accountIdValue]
            );

            if (isset($document)) {
                $document->update([
                    'outlines' => $result['outlines'] ?? [],
                    'faqs' => $result['faqs'] ?? [],
                ]);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('Error fetching document outline and FAQs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'document_id' => $documentId,
                'provider' => $provider,
            ]);

            return [
                'outlines' => [],
                'faqs' => [],
            ];
        }
    }
}
