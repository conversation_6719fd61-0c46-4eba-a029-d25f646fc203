<?php

namespace App\Services\V1\RAG;

use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Enums\RagServiceProviderType;
use App\Models\RagDocument;
use App\Models\User;
use App\Models\UserRagAccount;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class UserRagAccountService implements UserRagAccountServiceInterface
{
    /**
     * The RAG service.
     *
     * @var RagServiceInterface
     */
    protected $ragService;

    /**
     * Create a new user RAG account service instance.
     */
    public function __construct(RagServiceInterface $ragService)
    {
        $this->ragService = $ragService;
    }

    /**
     * Link a RAG account to a user.
     *
     * @param  User  $user  The user to link the account to
     * @param  string  $provider  The RAG service provider
     * @param  array  $accountData  Account data to store
     * @return UserRagAccount The linked account
     */
    public function linkAccount(User $user, string $provider, array $accountData): UserRagAccount
    {
        $collectionName = $accountData['collection_name'] ?? Str::slug("{$user->id}_{$user->name}_".Str::random(8));

        return UserRagAccount::updateOrCreate(
            [
                'user_id' => $user->id,
                'provider' => $provider,
                'collection_name' => $collectionName,
            ],
            [
                'collection_id' => $accountData['collection_id'] ?? null,
                'api_key' => $accountData['api_key'] ?? null,
                'account_id' => $accountData['account_id'] ?? null,
                'collection_metadata' => $accountData['collection_metadata'] ?? [],
                'settings' => $accountData['settings'] ?? [],
                'is_active' => true,
            ]
        );
    }

    /**
     * Unlink a RAG account from a user.
     *
     * @param  User  $user  The user to unlink from
     * @param  string  $provider  The RAG service provider
     * @param  string|null  $collectionId  Optional collection ID
     * @return bool True if succeeded, false otherwise
     */
    public function unlinkAccount(User $user, string $provider, ?string $collectionId = null): bool
    {
        $query = $user->ragAccounts()
            ->where('provider', $provider);

        if ($collectionId) {
            $query->where('collection_id', $collectionId);
        }

        return $query->update(['is_active' => false]);
    }

    /**
     * Get users by RAG service provider.
     *
     * @param  string  $provider  The RAG service provider
     * @return Collection Collection of users
     */
    public function getUsersByProvider(string $provider): Collection
    {
        return User::whereHas('ragAccounts', function ($query) use ($provider) {
            $query->where('provider', $provider)
                ->where('is_active', true);
        })->get();
    }

    /**
     * Update RAG account data.
     *
     * @param  UserRagAccount  $account  The account to update
     * @param  array  $data  New data to set
     * @return bool True if succeeded, false otherwise
     */
    public function updateAccountData(UserRagAccount $account, array $data): bool
    {
        return $account->update([
            'collection_name' => $data['collection_name'] ?? $account->collection_name,
            'api_key' => $data['api_key'] ?? $account->api_key,
            'account_id' => $data['account_id'] ?? $account->account_id,
            'collection_id' => $data['collection_id'] ?? $account->collection_id,
            'collection_metadata' => $data['collection_metadata'] ?? $account->collection_metadata,
            'settings' => $data['settings'] ?? $account->settings,
            'is_active' => $data['is_active'] ?? $account->is_active,
        ]);
    }

    /**
     * Add a document to a user's RAG account.
     *
     * @param  UserRagAccount  $account  The account to add the document to
     * @param  string  $filePath  Path to the document
     * @param  string  $fileName  Original file name
     * @param  array  $metadata  Additional metadata
     * @return RagDocument|null The created document or null on failure
     */
    public function addDocument(UserRagAccount $account, string $filePath, string $fileName, array $metadata = []): ?RagDocument
    {
        $adapter = $this->ragService->getAdapter($account->provider);

        if (! $adapter) {
            Log::error('No adapter registered for provider', [
                'provider' => $account->provider,
            ]);

            return null;
        }


        $apiKey = $account->getApiKey();
        $accountId = $account->getAccountId();

        if (! $apiKey || ! $accountId) {
            Log::error('Missing API credentials for provider in configuration', [
                'provider' => $account->provider,
            ]);

            return null;
        }

        $documentId = Str::uuid().'_'.$fileName;
        $storagePath = 'rag_documents/'.$account->user_id.'/'.$documentId;
        Storage::disk('local')->put($storagePath, file_get_contents($filePath));

        try {

            $collectionId = $account->collection_id;

            $result = $adapter->uploadDocument(
                $collectionId,
                $filePath,
                $documentId,
                ['api_key' => $apiKey, 'account_id' => $accountId]
            );

            return RagDocument::create([
                'user_rag_account_id' => $account->id,
                'document_id' => $documentId,
                'original_filename' => $fileName,
                'file_path' => $storagePath,
                'status' => 'completed',
                'metadata' => array_merge($metadata, $result ?? []),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to upload document to RAG service', [
                'error' => $e->getMessage(),
                'file' => $fileName,
            ]);

            return null;
        }
    }

    /**
     * Get all documents for a user's RAG account.
     *
     * @param  UserRagAccount  $account  The account to get documents for
     * @return Collection Collection of documents
     */
    public function getDocuments(UserRagAccount $account): Collection
    {
        return $account->documents;
    }

    /**
     * Create a new user RAG account.
     *
     * @param  User  $user  The user
     * @param  RagServiceProviderType  $provider  The RAG service provider
     * @param  array  $accountData  Account data
     * @return UserRagAccount The created user RAG account
     */
    public function createAccount(
        User $user,
        RagServiceProviderType $provider,
        array $accountData
    ): UserRagAccount {
        return $this->ragService->createUserRagAccount($user, $provider, $accountData);
    }

    /**
     * Get a user's RAG account.
     *
     * @param  User  $user  The user
     * @param  string|null  $provider  The RAG service provider
     * @return UserRagAccount|null The user RAG account
     */
    public function getAccount(User $user, ?string $provider = null): ?UserRagAccount
    {
        $provider = $provider ?: Config::get('rag.default', RagServiceProviderType::ChatBees->value);

        return $user->ragAccounts()
            ->where('provider', $provider)
            ->where('is_active', true)
            ->first();
    }

    /**
     * List a user's RAG documents.
     *
     * @param  User  $user  The user
     * @param  string|null  $provider  The RAG service provider
     * @return \Illuminate\Database\Eloquent\Collection The user's RAG documents
     */
    public function listDocuments(User $user, ?string $provider = null)
    {
        $account = $this->getAccount($user, $provider);

        if (! $account) {
            return collect();
        }

        return $account->documents;
    }

    /**
     * Get a user's RAG document.
     *
     * @param  User  $user  The user
     * @param  string  $documentId  The document UUID
     * @param  string|null  $provider  The RAG service provider
     * @return RagDocument|null The document
     */
    public function getDocument(User $user, string $documentId, ?string $provider = null): ?RagDocument
    {
        $account = $this->getAccount($user, $provider);

        if (! $account) {
            return null;
        }

        return $account->documents()->where('uuid', $documentId)->first();
    }

    /**
     * Delete a user's RAG document.
     *
     * @param  User  $user  The user
     * @param  string  $documentId  The document UUID
     * @param  string|null  $provider  The RAG service provider
     * @return bool Whether the deletion was successful
     */
    public function deleteDocument(User $user, string $documentId, ?string $provider = null): bool
    {
        $document = $this->getDocument($user, $documentId, $provider);

        if (! $document) {
            return false;
        }

        $account = $document->userRagAccount;
        $adapter = $this->ragService->getAdapter($account->provider);

        if (! $adapter) {
            Log::error('No adapter registered for provider', [
                'provider' => $account->provider,
            ]);

            return false;
        }

        try {
            $adapter->deleteDocument($account->collection_id, $document->document_id);
        } catch (\Exception $e) {
            Log::error('Failed to delete document from RAG service', [
                'error' => $e->getMessage(),
                'document_id' => $document->id,
            ]);

            return false;
        }

        return $document->delete();
    }

    /**
     * Upload a document to the RAG service.
     *
     * @param  User  $user  The user
     * @param  string  $filePath  The file path
     * @param  string  $fileName  The file name
     * @param  array  $metadata  Additional metadata
     * @param  string|null  $provider  The RAG service provider
     * @return RagDocument|null The uploaded document
     */
    public function uploadDocument(
        User $user,
        string $filePath,
        string $fileName,
        array $metadata = [],
        ?string $provider = null
    ): ?RagDocument {
        return $this->ragService->uploadDocument($user, $filePath, $fileName, $metadata, $provider);
    }

    /**
     * Ask a question using the RAG service.
     *
     * @param  User  $user  The user
     * @param  string  $question  The question
     * @param  string|null  $documentId  The document ID
     * @param  array  $historyMessages  Previous chat history
     * @param  string|null  $provider  The RAG service provider
     * @param  int|null  $accountId  The specific collection account ID to use
     * @return array Answer and references
     */
    public function ask(
        User $user,
        string $question,
        ?string $documentId = null,
        array $historyMessages = [],
        ?string $provider = null,
        ?int $accountId = null
    ): array {
        return $this->ragService->ask(
            $user,
            $question,
            $documentId,
            $historyMessages,
            $provider,
            $accountId
        );
    }

    /**
     * Get document outlines and FAQs from the RAG service.
     *
     * @param  User  $user  The user
     * @param  string  $documentId  The document ID
     * @param  string|null  $provider  The RAG service provider
     * @return array Outlines and FAQs
     */
    public function getDocumentOutlineFaq(
        User $user,
        string $documentId,
        ?string $provider = null
    ): array {
        return $this->ragService->getDocumentOutlineFaq($user, $documentId, $provider);
    }
}
