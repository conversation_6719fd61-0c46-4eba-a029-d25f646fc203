<?php

namespace App\Services\V1;

use App\Contracts\Services\MediaServiceInterface;
use App\Models\Media;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MediaService implements MediaServiceInterface
{
    public function store(UploadedFile $file, User $user, ?string $disk = null, bool $isPrivate = false): Media
    {
        if ($this->shouldUseSocialMediaStorage()) {
            return $this->storeViaSocialMedia($file, $user, $isPrivate);
        }

        $disk = $disk ?: Config::get('media.default_disk', 'public');
        $filename = $this->generateUniqueFilename($file);
        $path = $this->generatePath($user->id, $filename);

        $storedPath = Storage::disk($disk)->putFileAs(
            dirname($path),
            $file,
            basename($path)
        );

        return Media::create([
            'user_id' => $user->id,
            'path' => $storedPath,
            'name' => $file->getClientOriginalName(),
            'disk' => $disk,
            'mime' => $file->getMimeType(),
            'size' => $file->getSize(),
            'is_private' => $isPrivate,
        ]);
    }

    public function storeFromUrl(string $url, User $user, ?string $disk = null, bool $isPrivate = false, ?string $name = null): Media
    {
        if ($this->shouldUseSocialMediaStorage()) {
            return $this->storeFromUrlViaSocialMedia($url, $user, $isPrivate, $name);
        }

        $contents = file_get_contents($url);
        if ($contents === false) {
            throw new \Exception('Unable to download file from URL');
        }

        $disk = $disk ?: Config::get('media.default_disk', 'public');
        $filename = $name ?? basename(parse_url($url, PHP_URL_PATH)) ?? 'file-'.time();
        $filename = $this->sanitizeFilename($filename);
        $path = $this->generatePath($user->id, $filename);

        Storage::disk($disk)->put($path, $contents);

        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($contents);

        return Media::create([
            'user_id' => $user->id,
            'path' => $path,
            'name' => $filename,
            'disk' => $disk,
            'mime' => $mimeType ?: null,
            'size' => strlen($contents),
            'is_private' => $isPrivate,
        ]);
    }

    public function getUserMedia(User $user, int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = $user->media();

        if (isset($filters['mime_type'])) {
            $query->where('mime', 'like', $filters['mime_type'].'/%');
        }

        if (isset($filters['is_private'])) {
            $query->where('is_private', $filters['is_private']);
        }

        if (isset($filters['search'])) {
            $query->where('name', 'like', '%'.$filters['search'].'%');
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    public function getByUuid(string $uuid): ?Media
    {
        return Media::where('uuid', $uuid)->first();
    }

    public function moveToDisk(Media $media, string $newDisk): bool
    {
        try {
            if ($media->external_url) {
                return false;
            }

            $contents = Storage::disk($media->disk)->get($media->path);
            Storage::disk($newDisk)->put($media->path, $contents);
            Storage::disk($media->disk)->delete($media->path);

            $media->update(['disk' => $newDisk]);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function getUrl(Media $media, ?int $expirationMinutes = null): string
    {
        if ($media->external_url) {
            return $media->external_url;
        }

        $storage = Storage::disk($media->disk);

        if ($media->is_private && $expirationMinutes) {
            return $storage->temporaryUrl(
                $media->path,
                now()->addMinutes($expirationMinutes)
            );
        }

        return $storage->url($media->path);
    }

    public function exists(Media $media): bool
    {
        if ($media->external_url) {
            return true;
        }

        return Storage::disk($media->disk)->exists($media->path);
    }

    public function getMetadata(Media $media): array
    {
        $metadata = [
            'id' => $media->id,
            'uuid' => $media->uuid,
            'name' => $media->name,
            'path' => $media->path,
            'disk' => $media->disk,
            'mime' => $media->mime,
            'size' => $media->size,
            'size_mb' => $media->getSizeInMB(),
            'is_private' => $media->is_private,
            'is_image' => $media->isImage(),
            'is_video' => $media->isVideo(),
            'url' => $this->getUrl($media),
            'exists' => $this->exists($media),
            'created_at' => $media->created_at,
            'updated_at' => $media->updated_at,
        ];

        if ($media->isImage() && $this->exists($media) && ! $media->external_url) {
            try {
                $fullPath = Storage::disk($media->disk)->path($media->path);
                if (function_exists('getimagesize')) {
                    $imageInfo = getimagesize($fullPath);
                    if ($imageInfo) {
                        $metadata['width'] = $imageInfo[0];
                        $metadata['height'] = $imageInfo[1];
                    }
                }
            } catch (\Exception $e) {
                // Ignore image metadata errors
            }
        }

        return $metadata;
    }

    public function delete(Media $media): bool
    {
        try {
            if ($media->external_id) {
                $this->deleteFromSocialMedia($media);
            }

            if (! $media->external_id && Storage::disk($media->disk)->exists($media->path)) {
                Storage::disk($media->disk)->delete($media->path);
            }

            return $media->delete();
        } catch (\Exception $e) {
            return false;
        }
    }

    private function generateUniqueFilename(UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = $this->sanitizeFilename($name);

        return $name.'_'.time().'_'.Str::random(8).($extension ? '.'.$extension : '');
    }

    private function sanitizeFilename(string $filename): string
    {
        return preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);
    }

    private function generatePath(int $userId, string $filename): string
    {
        $year = date('Y');
        $month = date('m');

        return "media/{$userId}/{$year}/{$month}/{$filename}";
    }

    protected function shouldUseSocialMediaStorage(): bool
    {
        return Config::get('media.use_social_media_storage', false);
    }

    protected function storeViaSocialMedia(UploadedFile $file, User $user, bool $isPrivate): Media
    {
        $socialMediaService = socialMediaService();
        $filename = $file->getClientOriginalName();

        $result = $socialMediaService->uploadMedia($file, $filename, null, $user);

        if (! $result) {
            throw new \Exception('Failed to upload to social media service');
        }

        $adapter = Config::get('media.social_media_storage.adapter', 'ayrshare');

        $mediaData = [
            'user_id' => $user->id,
            'name' => $filename,
            'disk' => $adapter,
            'mime' => $file->getMimeType(),
            'size' => $file->getSize(),
            'is_private' => $isPrivate,
            'external_id' => $result['media_id'],
            'external_url' => $result['media_url'],
            'path' => $result['media_url'] ?? '',
        ];

        if (Config::get('media.social_media_storage.store_locally', true)) {
            $localDisk = Config::get('media.social_media_storage.local_disk', 'public');
            $localFilename = $this->generateUniqueFilename($file);
            $localPath = $this->generatePath($user->id, $localFilename);

            Storage::disk($localDisk)->putFileAs(
                dirname($localPath),
                $file,
                basename($localPath)
            );

            $mediaData['path'] = $localPath;
            $mediaData['disk'] = $localDisk;
        }

        return Media::create($mediaData);
    }

    protected function storeFromUrlViaSocialMedia(string $url, User $user, bool $isPrivate, ?string $name): Media
    {
        $contents = file_get_contents($url);
        if ($contents === false) {
            throw new \Exception('Unable to download file from URL');
        }

        $filename = $name ?? basename(parse_url($url, PHP_URL_PATH)) ?? 'file-'.time();
        $filename = $this->sanitizeFilename($filename);

        $tempFile = tmpfile();
        fwrite($tempFile, $contents);
        $tempPath = stream_get_meta_data($tempFile)['uri'];

        $finfo = new \finfo(FILEINFO_MIME_TYPE);
        $mimeType = $finfo->buffer($contents);

        $uploadedFile = new UploadedFile($tempPath, $filename, $mimeType, null, true);

        try {
            $media = $this->storeViaSocialMedia($uploadedFile, $user, $isPrivate);

            return $media;
        } finally {
            fclose($tempFile);
        }
    }

    protected function deleteFromSocialMedia(Media $media): bool
    {
        if (! $media->external_id) {
            return true;
        }

        $socialMediaService = socialMediaService();

        return $socialMediaService->deleteMedia($media->external_id, $media->user);
    }
}
