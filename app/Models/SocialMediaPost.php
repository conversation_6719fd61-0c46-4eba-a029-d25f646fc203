<?php

namespace App\Models;

use App\Enums\SocialPlatform;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SocialMediaPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'content',
        'post_group_id',
        'post_data',
        'platforms',
        'media_urls',
        'scheduled_at',
        'status',
    ];

    protected $casts = [
        'platforms' => 'array',
        'media_urls' => 'array',
        'post_data' => 'array',
        'scheduled_at' => 'datetime',
    ];

    /**
     * Get the user that owns the post.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Validate platform values against the enum.
     */
    public function validatePlatforms(): bool
    {
        if (! is_array($this->platforms)) {
            return false;
        }

        $validPlatforms = SocialPlatform::values();

        foreach ($this->platforms as $platform) {
            if (! in_array($platform, $validPlatforms)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get platform labels for display.
     */
    public function getPlatformLabels(): array
    {
        if (! is_array($this->platforms)) {
            return [];
        }

        $labels = [];

        foreach ($this->platforms as $platform) {
            $enum = SocialPlatform::tryFrom($platform);
            if ($enum) {
                $labels[$platform] = $enum->label();
            } else {
                $labels[$platform] = ucfirst($platform);
            }
        }

        return $labels;
    }
}
