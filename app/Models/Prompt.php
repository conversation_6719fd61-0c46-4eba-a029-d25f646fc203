<?php

namespace App\Models;

use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Prompt extends Model
{
    protected $fillable = [
        'name',
        'machine_name',
        'type',
        'platform',
        'prompt_text',
    ];

    protected $casts = [
        'type' => PromptType::class,
        'platform' => SocialPlatform::class,
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($prompt) {
            if (empty($prompt->machine_name)) {
                $prompt->machine_name = Str::slug($prompt->name);
            }
        });

        static::updating(function ($prompt) {
            if ($prompt->isDirty('name')) {
                $prompt->machine_name = Str::slug($prompt->name);
            }
        });
    }

    public static function findByTypeAndPlatform(PromptType $type, SocialPlatform $platform): ?self
    {
        return static::where('type', $type->value)
            ->where('platform', $platform->value)
            ->first();
    }
}
