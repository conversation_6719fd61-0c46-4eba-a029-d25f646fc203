<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Media extends Model
{
    use HasFactory;

    protected $fillable = [
        'uuid',
        'user_id',
        'path',
        'name',
        'disk',
        'mime',
        'size',
        'is_private',
        'external_id',
        'external_url',
    ];

    protected $casts = [
        'size' => 'integer',
        'is_private' => 'boolean',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->uuid)) {
                $model->uuid = (string) Str::uuid();
            }
        });

        static::deleting(function ($model) {
            Storage::disk($model->disk)->delete($model->path);
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getUrlAttribute(): string
    {
        if ($this->external_url) {
            return $this->external_url;
        }

        return Storage::disk($this->disk)->url($this->path);
    }

    public function getFullPathAttribute(): string
    {
        return Storage::disk($this->disk)->path($this->path);
    }

    public function exists(): bool
    {
        return Storage::disk($this->disk)->exists($this->path);
    }

    public function getSizeInMB(): float
    {
        return round($this->size / 1024 / 1024, 2);
    }

    public function isImage(): bool
    {
        return str_starts_with($this->mime ?? '', 'image/');
    }

    public function isVideo(): bool
    {
        return str_starts_with($this->mime ?? '', 'video/');
    }
}
