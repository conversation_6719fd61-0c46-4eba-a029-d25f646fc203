<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class RagDocument extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_rag_account_id',
        'uuid',
        'document_id',
        'original_filename',
        'file_path',
        'file_size',
        'mime_type',
        'status',
        'metadata',
        'outlines',
        'faqs',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'metadata' => 'array',
        'outlines' => 'array',
        'faqs' => 'array',
        'file_size' => 'integer',
    ];

    /**
     * Boot function from Laravel.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($document) {
            if (empty($document->uuid)) {
                $document->uuid = (string) Str::uuid();
            }
        });
    }

    /**
     * Get the RAG account that owns the document.
     */
    public function ragAccount()
    {
        return $this->belongsTo(UserRagAccount::class, 'user_rag_account_id');
    }

    /**
     * Get the user that owns the document.
     */
    public function user()
    {
        return $this->hasOneThrough(
            User::class,
            UserRagAccount::class,
            'id',
            'id',
            'user_rag_account_id',
            'user_id'
        );
    }
}
