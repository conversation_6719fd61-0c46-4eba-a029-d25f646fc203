<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserRagAccount extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'user_id',
        'provider',
        'collection_id',
        'api_key',
        'account_id',
        'collection_metadata',
        'settings',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'collection_metadata' => 'array',
        'settings' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the RAG account.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the documents associated with this account.
     */
    public function documents()
    {
        return $this->hasMany(RagDocument::class);
    }

    /**
     * Get accounts by provider.
     *
     * @param  string  $provider
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public static function byProvider($provider)
    {
        return static::where('provider', $provider)
            ->where('is_active', true);
    }
}
