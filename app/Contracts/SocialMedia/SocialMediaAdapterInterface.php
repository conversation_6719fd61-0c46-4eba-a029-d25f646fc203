<?php

namespace App\Contracts\SocialMedia;

interface SocialMediaAdapterInterface
{
    /**
     * Create a user account on the platform.
     *
     * @return array|null Response data or null on failure
     */
    public function createUser(array $userData): ?array;

    /**
     * Generate an authentication token for a user.
     *
     * @return string|null Token or null on failure
     */
    public function generateToken(string $userId): ?string;

    /**
     * Publish a post on the platform.
     *
     * @return array|bool Response data or false on failure
     */
    public function publishPost(array $postData): array|bool;

    /**
     * Get posts from the platform.
     *
     * @param  string  $userId  The user ID to get posts for
     * @return array Posts data or empty array on failure
     */
    public function getPosts(string $userId): array;

    /**
     * Get posts from the platform with filters and pagination.
     *
     * @param  string  $userId  The user ID to get posts for
     * @param  array  $filters  Filters to apply (platform, startDate, endDate, status, etc.)
     * @param  int  $perPage  Number of posts per page
     * @param  int  $page  Page number
     * @return array Posts data with pagination info
     */
    public function getPostsWithFilters(string $userId, array $filters = [], int $perPage = 25, int $page = 1): array;

    /**
     * Check if a user exists on the platform.
     */
    public function userExists(string $userId): bool;

    /**
     * Schedule a post on the platform.
     */
    public function schedulePost(array $postData): bool;

    /**
     * Get analytics for a post.
     */
    public function getAnalytics(string $postId): array;

    /**
     * Get the social media URL for a user.
     */
    public function getSocialUrl(?string $token = null): ?string;

    /**
     * Delete a post on the platform.
     *
     * @param  string  $postId  The ID of the post to delete
     * @return bool True if successfully deleted, false otherwise
     */
    public function deletePost(string $postId): bool;

    /**
     * Upload media file
     *
     * @param  mixed  $file  File to upload (binary or base64)
     * @param  string|null  $fileName  File name
     * @param  string|null  $description  File description
     * @param  string|null  $profileKey  Profile key for user context
     * @return array|null Media upload response or null on failure
     */
    public function uploadMedia($file, ?string $fileName = null, ?string $description = null, ?string $profileKey = null): array;

    /**
     * Get upload URL for large files
     *
     * @param  string  $fileName  File name
     * @param  string|null  $contentType  Content type
     * @param  string|null  $profileKey  Profile key for user context
     * @return array|null Upload URL response or null on failure
     */
    public function getUploadUrl(string $fileName, ?string $contentType = null, ?string $profileKey = null): array;

    /**
     * Get all media from gallery
     *
     * @param  string|null  $profileKey  Profile key for user context
     * @return array Media gallery
     */
    public function getMediaGallery(?string $profileKey = null): array;

    /**
     * Resize media
     *
     * @param  string  $mediaId  Media ID or URL
     * @param  int  $width  Target width
     * @param  int  $height  Target height
     * @param  string|null  $profileKey  Profile key for user context
     * @return array|null Resize response or null on failure
     */
    public function resizeMedia(string $mediaId, int $width, int $height, ?string $profileKey = null): array;

    /**
     * Get media metadata
     *
     * @param  string  $mediaId  Media ID or URL
     * @param  string|null  $profileKey  Profile key for user context
     * @return array|null Media metadata or null on failure
     */
    public function getMediaMetadata(string $mediaId, ?string $profileKey = null): array;

    /**
     * Verify media URL exists
     *
     * @param  string  $mediaUrl  Media URL to verify
     * @param  string|null  $profileKey  Profile key for user context
     * @return bool True if exists, false otherwise
     */
    public function verifyMediaUrl(string $mediaUrl, ?string $profileKey = null): bool;

    /**
     * Delete media
     *
     * @param  string  $mediaId  Media ID
     * @param  string|null  $profileKey  Profile key for user context
     * @return bool Success or failure
     */
    public function deleteMedia(string $mediaId, ?string $profileKey = null): bool;

    /**
     * Generate JWT and URL
     *
     * @param  string  $profileKey  Profile key for user context
     * @param  array  $options  Additional options
     * @return array|null JWT and URL or null on failure
     */
    public function generateJwtAndUrl(string $profileKey, array $options = []): ?array;
}
