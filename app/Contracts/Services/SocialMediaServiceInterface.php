<?php

namespace App\Contracts\Services;

use App\Enums\SocialPlatformType;
use App\Models\SocialMediaPost;
use App\Models\User;
use App\Models\UserSocialAccount;

interface SocialMediaServiceInterface
{
    /**
     * Register a social media adapter for a platform
     */
    public function registerAdapter(string $platform, \App\Contracts\SocialMedia\SocialMediaAdapterInterface $adapter): self;

    /**
     * Get the adapter for a specific platform
     */
    public function getAdapter(?string $platform = null): ?\App\Contracts\SocialMedia\SocialMediaAdapterInterface;

    /**
     * Generate a token for a user.
     * Supports both User object with platform, and direct userId string.
     *
     * @param  User|string  $userOrUserId  Either a User object or a platform user ID string
     * @param  string|null  $platform  Optional platform identifier (only used when first param is User)
     * @return string|null Token or null on failure
     */
    public function generateToken(User|string $userOrUserId, ?string $platform = null): ?string;

    /**
     * Create or update a social media account for a user
     */
    public function createUserSocialAccount(
        User $user,
        SocialPlatformType $platform,
        array $accountData
    ): ?UserSocialAccount;

    /**
     * Create a user account on the social media platform.
     *
     * @param  array  $userData  User data for creating account
     * @return array|null Response data or null on failure
     */
    public function createUser(array $userData): ?array;

    /**
     * Publish a post to social media platforms.
     *
     * @param  array  $postData  The post data
     * @param  User  $user  The user who owns the post
     * @return SocialMediaPost|null The created post or null on failure
     */
    public function publishPost(array $postData, User $user): ?SocialMediaPost;

    /**
     * Get posts for a user.
     *
     * @param  User  $user  The user to get posts for
     * @param  array  $filters  Filters to apply to the posts
     * @param  int  $perPage  Number of posts per page
     * @param  int  $page  Page number
     * @return array Posts data with pagination info
     */
    public function getPosts(User $user, array $filters = [], int $perPage = 20, int $page = 1): array;

    /**
     * Get the social media URL for a user
     *
     * @param  string|null  $token  User token
     * @return string|null URL or null on failure
     */
    public function getSocialUrl(?string $token = null): ?string;

    /**
     * Delete a post from social media platforms.
     *
     * @param  SocialMediaPost  $post  The post to delete
     * @return bool Success or failure
     */
    public function deletePost(SocialMediaPost $post): bool;

    /**
     * Generate token and social URL for a user in one call
     *
     * @param  User  $user  The user to generate token and URL for
     * @param  string|null  $platform  Optional platform identifier
     * @return array|null Array with token and url or null on failure
     */
    public function generateTokenAndUrl(User $user, ?string $platform = null): ?array;

    /**
     * Upload media file
     *
     * @param  mixed  $file  File to upload
     * @param  string|null  $fileName  File name
     * @param  string|null  $description  File description
     * @param  User|null  $user  User for profile context
     * @return array|null Media upload response or null on failure
     */
    public function uploadMedia($file, ?string $fileName = null, ?string $description = null, ?User $user = null): ?array;

    /**
     * Get upload URL for large files
     *
     * @param  string  $fileName  File name
     * @param  string|null  $contentType  Content type
     * @param  User|null  $user  User for profile context
     * @return array|null Upload URL response or null on failure
     */
    public function getUploadUrl(string $fileName, ?string $contentType = null, ?User $user = null): ?array;

    /**
     * Get all media from gallery
     *
     * @param  User|null  $user  User for profile context
     * @return array Media gallery
     */
    public function getMediaGallery(?User $user = null): array;

    /**
     * Resize media
     *
     * @param  string  $mediaId  Media ID
     * @param  int  $width  Target width
     * @param  int  $height  Target height
     * @param  User|null  $user  User for profile context
     * @return array|null Resize response or null on failure
     */
    public function resizeMedia(string $mediaId, int $width, int $height, ?User $user = null): ?array;

    /**
     * Get media metadata
     *
     * @param  string  $mediaId  Media ID
     * @param  User|null  $user  User for profile context
     * @return array|null Media metadata or null on failure
     */
    public function getMediaMetadata(string $mediaId, ?User $user = null): ?array;

    /**
     * Verify media URL exists
     *
     * @param  string  $mediaUrl  Media URL to verify
     * @param  User|null  $user  User for profile context
     * @return bool True if exists, false otherwise
     */
    public function verifyMediaUrl(string $mediaUrl, ?User $user = null): bool;

    /**
     * Delete media
     *
     * @param  string  $mediaId  Media ID
     * @param  User|null  $user  User for profile context
     * @return bool Success or failure
     */
    public function deleteMedia(string $mediaId, ?User $user = null): bool;
}
