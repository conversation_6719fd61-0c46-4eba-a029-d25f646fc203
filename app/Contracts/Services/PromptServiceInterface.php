<?php

namespace App\Contracts\Services;

use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Models\Prompt;
use Illuminate\Support\Collection;

interface PromptServiceInterface
{
    public function getPrompt(PromptType $type, SocialPlatform $platform): ?Prompt;

    public function getPromptText(PromptType $type, SocialPlatform $platform): ?string;

    public function createPrompt(array $data): Prompt;

    public function updatePrompt(Prompt $prompt, array $data): Prompt;

    public function deletePrompt(Prompt $prompt): bool;

    public function getAllPrompts(): Collection;

    public function getPromptsByType(PromptType $type): Collection;

    public function getPromptsByPlatform(SocialPlatform $platform): Collection;
}
