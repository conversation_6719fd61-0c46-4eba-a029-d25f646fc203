<?php

namespace App\Contracts\Services;

use App\Enums\RagServiceProviderType;
use App\Models\RagDocument;
use App\Models\User;
use App\Models\UserRagAccount;

interface RagServiceInterface
{
    /**
     * Register a RAG service adapter
     *
     * @param  string  $provider  Provider name
     * @param  object  $adapter  Provider adapter instance
     */
    public function registerAdapter(string $provider, $adapter): self;

    /**
     * Get the provider adapter
     *
     * @param  string|null  $provider  Provider name
     * @return object|null The adapter or null if not found
     */
    public function getAdapter(?string $provider = null): ?object;

    /**
     * Create a user account on the RAG service provider
     *
     * @param  User  $user  The user to create an account for
     * @param  RagServiceProviderType  $provider  The RAG service provider
     * @param  array  $accountData  Additional account data
     * @return UserRagAccount The created user RAG account
     */
    public function createUserRagAccount(
        User $user,
        RagServiceProviderType $provider,
        array $accountData
    ): UserRagAccount;

    /**
     * Upload a document to the RAG service
     *
     * @param  User  $user  The user uploading the document
     * @param  string  $filePath  The path to the document
     * @param  string  $fileName  The original filename
     * @param  array  $metadata  Additional metadata
     * @param  string|null  $provider  The RAG service provider
     * @return RagDocument|null The created document or null on failure
     */
    public function uploadDocument(
        User $user,
        string $filePath,
        string $fileName,
        array $metadata = [],
        ?string $provider = null
    ): ?RagDocument;

    /**
     * Ask a question using the RAG service
     *
     * @param  User  $user  The user asking the question
     * @param  string  $question  The question to ask
     * @param  string|null  $documentId  Specific document ID to query (optional)
     * @param  array  $historyMessages  Previous chat history (optional)
     * @param  string|null  $provider  The RAG service provider
     * @return array Answer and references
     */
    public function ask(
        User $user,
        string $question,
        ?string $documentId = null,
        array $historyMessages = [],
        ?string $provider = null
    ): array;

    /**
     * Get document outlines and FAQs from the RAG service
     *
     * @param  User  $user  The user
     * @param  string  $documentId  The document ID
     * @param  string|null  $provider  The RAG service provider
     * @return array Outlines and FAQs
     */
    public function getDocumentOutlineFaq(
        User $user,
        string $documentId,
        ?string $provider = null
    ): array;
}
