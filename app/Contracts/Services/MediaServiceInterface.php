<?php

namespace App\Contracts\Services;

use App\Models\Media;
use App\Models\User;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;

interface MediaServiceInterface
{
    /**
     * Store an uploaded file and create media record
     */
    public function store(UploadedFile $file, User $user, ?string $disk = null, bool $isPrivate = false): Media;

    /**
     * Store file from URL and create media record
     */
    public function storeFromUrl(string $url, User $user, ?string $disk = null, bool $isPrivate = false, ?string $name = null): Media;

    /**
     * Get user's media files with pagination
     */
    public function getUserMedia(User $user, int $perPage = 15, array $filters = []): LengthAwarePaginator;

    /**
     * Get media by UUID
     */
    public function getByUuid(string $uuid): ?Media;

    /**
     * Delete media file and record
     */
    public function delete(Media $media): bool;

    /**
     * Move media to different disk
     */
    public function moveToDisk(Media $media, string $newDisk): bool;

    /**
     * Get media URL with expiration for private files
     */
    public function getUrl(Media $media, ?int $expirationMinutes = null): string;

    /**
     * Verify if media file exists on disk
     */
    public function exists(Media $media): bool;

    /**
     * Get media metadata
     */
    public function getMetadata(Media $media): array;
}
