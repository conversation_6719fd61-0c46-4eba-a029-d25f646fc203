<?php

namespace App\Contracts\Services;

use App\Models\RagDocument;
use App\Models\User;
use App\Models\UserRagAccount;
use Illuminate\Database\Eloquent\Collection;

interface UserRagAccountServiceInterface
{
    /**
     * Link a RAG account to a user.
     *
     * @param  User  $user  The user to link the account to
     * @param  string  $provider  The RAG service provider
     * @param  array  $accountData  Account data to store
     * @return UserRagAccount The linked account
     */
    public function linkAccount(User $user, string $provider, array $accountData): UserRagAccount;

    /**
     * Unlink a RAG account from a user.
     *
     * @param  User  $user  The user to unlink from
     * @param  string  $provider  The RAG service provider
     * @param  string|null  $collectionId  Optional collection ID
     * @return bool True if succeeded, false otherwise
     */
    public function unlinkAccount(User $user, string $provider, ?string $collectionId = null): bool;

    /**
     * Get users by RAG service provider.
     *
     * @param  string  $provider  The RAG service provider
     * @return Collection Collection of users
     */
    public function getUsersByProvider(string $provider): Collection;

    /**
     * Update RAG account data.
     *
     * @param  UserRagAccount  $account  The account to update
     * @param  array  $data  New data to set
     * @return bool True if succeeded, false otherwise
     */
    public function updateAccountData(UserRagAccount $account, array $data): bool;

    /**
     * Add a document to a user's RAG account.
     *
     * @param  UserRagAccount  $account  The account to add the document to
     * @param  string  $filePath  Path to the document
     * @param  string  $fileName  Original file name
     * @param  array  $metadata  Additional metadata
     * @return RagDocument|null The created document or null on failure
     */
    public function addDocument(UserRagAccount $account, string $filePath, string $fileName, array $metadata = []): ?RagDocument;

    /**
     * Get all documents for a user's RAG account.
     *
     * @param  UserRagAccount  $account  The account to get documents for
     * @return Collection Collection of documents
     */
    public function getDocuments(UserRagAccount $account): Collection;

    /**
     * Get a user's RAG account for a provider.
     *
     * @param  User  $user  The user
     * @param  string|null  $provider  The RAG service provider
     * @return UserRagAccount|null The user RAG account
     */
    public function getAccount(User $user, ?string $provider = null): ?UserRagAccount;
}
