<?php

namespace App\Enums;

enum RagServiceProviderType: string
{
    case ChatBees = 'chatbees';

    /**
     * Get all provider values
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human readable name
     */
    public function label(): string
    {
        return match ($this) {
            self::ChatBees => 'ChatBees',
        };
    }
}
