<?php

namespace App\Enums;

enum SocialPlatformType: string
{
    case SocialPilot = 'socialpilot';
    case AyrShare = 'ayrshare';

    /**
     * Get all platform values
     *
     * @return array<string>
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get human readable name
     */
    public function label(): string
    {
        return match ($this) {
            self::SocialPilot => 'Social Pilot',
            self::AyrShare => 'AyrShare',
        };
    }
}
