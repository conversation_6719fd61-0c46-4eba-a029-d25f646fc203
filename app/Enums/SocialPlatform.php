<?php

namespace App\Enums;

enum SocialPlatform: string
{
    case TWITTER = 'twitter';
    case FACEBOOK = 'facebook';
    case INSTAGRAM = 'instagram';
    case LINKEDIN = 'linkedin';
    case PINTEREST = 'pinterest';
    case BLUESKY = 'bluesky';
    case REDDIT = 'reddit';
    case TELEGRAM = 'telegram';
    case YOUTUBE = 'youtube';
    case TIKTOK = 'tiktok';
    case GOOGLE_BUSINESS = 'gmb';

    /**
     * Get the display name of the platform.
     */
    public function label(): string
    {
        return match ($this) {
            self::TWITTER => 'Twitter/X',
            self::FACEBOOK => 'Facebook',
            self::INSTAGRAM => 'Instagram',
            self::LINKEDIN => 'LinkedIn',
            self::PINTEREST => 'Pinterest',
            self::BLUESKY => 'Bluesky',
            self::REDDIT => 'Reddit',
            self::TELEGRAM => 'Telegram',
            self::YOUTUBE => 'YouTube',
            self::TIKTOK => 'TikTok',
            self::GOOGLE_BUSINESS => 'Google Business Profile',
        };
    }

    /**
     * Get all platforms as an array for frontend use.
     */
    public static function platformsForFrontend(): array
    {
        return array_map(
            fn (SocialPlatform $platform) => [
                'value' => $platform->value,
                'label' => $platform->label(),
            ],
            self::cases()
        );
    }

    /**
     * Get all platform values.
     */
    public static function values(): array
    {
        return array_map(
            fn (SocialPlatform $platform) => $platform->value,
            self::cases()
        );
    }
}
