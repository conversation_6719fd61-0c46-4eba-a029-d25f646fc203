<?php

namespace App\Enums;

enum PromptType: string
{
    case QUICK_POST = 'quick_post';
    case CAMPAIGN = 'campaign';

    public function label(): string
    {
        return match ($this) {
            self::QUICK_POST => 'Quick Post',
            self::CAMPAIGN => 'Campaign',
        };
    }

    public static function values(): array
    {
        return array_map(
            fn (PromptType $type) => $type->value,
            self::cases()
        );
    }

    public static function options(): array
    {
        return array_map(
            fn (PromptType $type) => [
                'value' => $type->value,
                'label' => $type->label(),
            ],
            self::cases()
        );
    }
}
