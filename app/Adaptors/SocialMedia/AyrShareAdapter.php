<?php

namespace App\Adaptors\SocialMedia;

use App\Contracts\SocialMedia\SocialMediaAdapterInterface;
use App\Enums\SocialPlatform;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class AyrShareAdapter implements SocialMediaAdapterInterface
{
    /**
     * Base API URL
     */
    protected string $baseUrl;

    /**
     * API key
     */
    protected ?string $apiKey;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->apiKey = Config::get('socialmedia.connections.ayrshare.api_key');
        $this->baseUrl = Config::get('socialmedia.connections.ayrshare.base_url', 'https://api.ayrshare.com/api');
    }

    /**
     * Create a user account on AyrShare.
     *
     * @return array|null Response data or null on failure
     */
    public function createUser(array $userData): ?array
    {
        try {
            $requiredFields = ['email', 'firstName', 'lastName'];
            foreach ($requiredFields as $field) {
                if (! isset($userData[$field]) || empty($userData[$field])) {
                    Log::error('AyrShare create user: Missing required field', [
                        'field' => $field,
                        'email' => $userData['email'] ?? 'unknown',
                    ]);

                    return null;
                }
            }

            $data = [
                'email' => $userData['email'],
                'title' => $userData['firstName'].' '.$userData['lastName'],
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl.'/profiles', $data);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['profileKey'])) {
                    return ['user_id' => $data['profileKey']];
                }

                Log::error('AyrShare user creation response has unexpected format', [
                    'email' => $userData['email'],
                    'response' => $data,
                ]);

                return null;
            }

            $errorData = $response->json();

            Log::error('Failed to create AyrShare user', [
                'email' => $userData['email'],
                'statusCode' => $response->status(),
                'error' => $errorData,
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while creating AyrShare user', [
                'email' => $userData['email'] ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Generate an authentication token for a user.
     *
     * @return string|null Token or null on failure
     */
    public function generateToken(string $userId): ?string
    {
        try {

            $domain = Config::get('socialmedia.connections.ayrshare.domain');
            $privateKey = Config::get('socialmedia.connections.ayrshare.private_key');

            if (! $domain || ! $privateKey) {
                Log::error('AyrShare domain or private key not configured', [
                    'userId' => $userId,
                    'domain_configured' => ! empty($domain),
                    'private_key_configured' => ! empty($privateKey),
                ]);

                return null;
            }

            $data = [
                'domain' => $domain,
                'privateKey' => $privateKey,
                'profileKey' => $userId,
            ];

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl.'/profiles/generateJWT', $data);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['token'])) {
                    return $data['token'];
                }

                return null;
            }

            $errorData = $response->json();
            $statusCode = $response->status();

            if ($statusCode === 401) {
                Log::error('Unauthorized access when generating AyrShare token', [
                    'userId' => $userId,
                ]);
            } elseif ($statusCode === 404) {
                Log::error('User not found when generating AyrShare token', [
                    'userId' => $userId,
                ]);
            } elseif ($statusCode === 400) {
                Log::error('Bad request when generating AyrShare token - likely private key formatting issue', [
                    'userId' => $userId,
                    'error' => $errorData,
                ]);
            } else {
                Log::error('Failed to generate AyrShare token', [
                    'userId' => $userId,
                    'statusCode' => $statusCode,
                    'error' => $errorData,
                ]);
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while generating AyrShare token', [
                'userId' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return null;
        }
    }

    /**
     * Check if a user exists on AyrShare.
     */
    public function userExists(string $userId): bool
    {
        try {

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Accept' => 'application/json',
            ])->get($this->baseUrl.'/profiles/generateJWT', [
                'profileKey' => $userId,
            ]);

            if ($response->successful()) {
                return true;
            }

            if ($response->status() === 404) {
                return false;
            }

            Log::error('Error checking if AyrShare user exists', [
                'userId' => $userId,
                'statusCode' => $response->status(),
                'error' => $response->json(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Exception while checking AyrShare user', [
                'userId' => $userId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function publishPost(array $postData): array
    {
        try {
            if (isset($postData['platforms']) && is_array($postData['platforms'])) {
                foreach ($postData['platforms'] as $platform) {
                    if (! SocialPlatform::tryFrom($platform)) {
                        Log::error('Invalid platform specified', [
                            'platform' => $platform,
                            'allowedPlatforms' => SocialPlatform::values(),
                        ]);

                        $postData['platforms'] = array_filter(
                            $postData['platforms'],
                            fn ($p) => SocialPlatform::tryFrom($p) !== null
                        );

                        if (empty($postData['platforms'])) {
                            throw new \Exception('No valid platforms specified');
                        }
                    }
                }
            }

            $payload = $this->buildPayload($postData);
            $request = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ]);

            if (isset($postData['profileKey'])) {
                $request->withHeader('Profile-Key', $postData['profileKey']);
            }

            $response = $request->post($this->baseUrl.'/post', $payload);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'post_id' => $data['id'] ?? null,
                    'platform_post_ids' => $data['postIds'] ?? [],
                    'status' => $data['status'] ?? 'published',
                    'scheduled_time' => $data['scheduleDate'] ?? null,
                    'platforms' => $postData['platforms'],
                    'raw_response' => $data,
                ];
            }

            $statusCode = $response->status();
            $errorData = $response->json();

            if ($statusCode === 401) {
                throw new \Exception('Unauthorized: Invalid API key or expired token');
            }

            if ($statusCode === 403) {
                throw new \Exception('Forbidden: User not connected to social media platform');
            }

            if ($statusCode === 404) {
                throw new \Exception('Profile not found: User profile does not exist');
            }

            if ($statusCode === 400) {
                $platformErrors = $this->parsePlatformErrors($errorData);

                if (! empty($platformErrors)) {
                    $exception = new \Exception('Platform linkage errors detected');
                    $exception->platformErrors = $platformErrors;
                    throw $exception;
                }

                $errorMessage = 'Bad request';
                if (isset($errorData['message'])) {
                    $errorMessage = $errorData['message'];
                } elseif (isset($errorData['error'])) {
                    $errorMessage = $errorData['error'];
                }

                if (strpos(strtolower($errorMessage), 'profile') !== false ||
                    strpos(strtolower($errorMessage), 'not found') !== false) {
                    throw new \Exception('Profile not found: '.$response->body());
                }

                throw new \Exception('Validation error: '.$response->body());
            }

            Log::error('Failed to publish AyrShare post', [
                'postData' => $postData,
                'statusCode' => $statusCode,
                'error' => $errorData,
            ]);

            throw new \Exception('Failed to publish post: HTTP '.$statusCode);
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Unauthorized') === 0 ||
                strpos($e->getMessage(), 'Forbidden') === 0 ||
                strpos($e->getMessage(), 'Profile not found') === 0 ||
                strpos($e->getMessage(), 'Platform linkage errors') === 0) {
                throw $e;
            }

            Log::error('Exception while publishing AyrShare post', [
                'postData' => $postData,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw new \Exception('Failed to publish post: '.$e->getMessage());
        }
    }

    private function parsePlatformErrors(array $errorData): array
    {
        $platformErrors = [];

        if (isset($errorData['posts']) && is_array($errorData['posts'])) {
            foreach ($errorData['posts'] as $post) {
                if (isset($post['errors']) && is_array($post['errors'])) {
                    foreach ($post['errors'] as $error) {
                        if (isset($error['platform']) && isset($error['message'])) {
                            $platform = strtolower($error['platform']);
                            $message = $error['message'];
                            $code = $error['code'] ?? null;

                            if (strpos(strtolower($message), 'not linked') !== false ||
                                $code === 156) {
                                $platformErrors[] = [
                                    'platform' => $platform,
                                    'error' => $message,
                                    'code' => 'NOT_LINKED',
                                    'action_required' => 'LINK_PLATFORM',
                                ];
                            } else {
                                $platformErrors[] = [
                                    'platform' => $platform,
                                    'error' => $message,
                                    'code' => 'PLATFORM_ERROR',
                                ];
                            }
                        }
                    }
                }
            }
        }

        return $platformErrors;
    }

    private function buildPayload(array $postData): array
    {
        $payload = [
            'post' => $postData['post'],
            'platforms' => $postData['platforms'],
        ];

        $optionalFields = [
            'mediaUrls', 'scheduleDate', 'youTubeOptions', 'linkedInOptions',
            'tiktokOptions', 'shortenLinks', 'muteVideo', 'autoHashtag',
            'boost', 'replyTo', 'lat', 'long', 'altText', 'priority',
            'poll', 'threadOptions',
        ];

        foreach ($optionalFields as $field) {
            if (isset($postData[$field]) && ! empty($postData[$field])) {
                $payload[$field] = $postData[$field];
            }
        }

        return $payload;
    }

    public function getPosts(string $userId): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Profile-Key' => $userId,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->get($this->baseUrl.'/history');

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'posts' => $data['history'] ?? [],
                    'count' => $data['count'] ?? 0,
                    'refId' => $data['refId'] ?? null,
                    'lastUpdated' => $data['lastUpdated'] ?? null,
                    'nextUpdate' => $data['nextUpdate'] ?? null,
                ];
            }

            Log::error('Failed to get AyrShare posts', [
                'userId' => $userId,
                'statusCode' => $response->status(),
                'error' => $response->json(),
            ]);

            return [
                'posts' => [],
                'count' => 0,
                'refId' => null,
                'lastUpdated' => null,
                'nextUpdate' => null,
            ];
        } catch (\Exception $e) {
            Log::error('Exception while getting AyrShare posts', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'posts' => [],
                'count' => 0,
                'refId' => null,
                'lastUpdated' => null,
                'nextUpdate' => null,
            ];
        }
    }

    public function getPostsWithFilters(string $userId, array $filters = [], int $perPage = 25, int $page = 1): array
    {
        try {
            $queryParams = [];

            if (isset($filters['platform'])) {
                $endpoint = "/history/{$filters['platform']}";
            } else {
                $endpoint = '/history';
            }

            if (isset($filters['startDate'])) {
                $queryParams['startDate'] = $filters['startDate'];
            }

            if (isset($filters['endDate'])) {
                $queryParams['endDate'] = $filters['endDate'];
            }

            if (isset($filters['status'])) {
                $queryParams['status'] = $filters['status'];
            }

            if (isset($filters['lastDays'])) {
                $queryParams['lastDays'] = $filters['lastDays'];
            }

            if (isset($filters['refId'])) {
                $queryParams['refId'] = $filters['refId'];
            }

            if (isset($filters['id'])) {
                $queryParams['id'] = $filters['id'];
            }

            $queryParams['limit'] = $perPage;

            if ($page > 1) {
                $queryParams['offset'] = ($page - 1) * $perPage;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Profile-Key' => $userId,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->get($this->baseUrl.$endpoint, $queryParams);

            Log::info('AyrShare posts response', [
                'userId' => $userId,
                'baseUrl' => $this->baseUrl,
                'apiKey' => $this->apiKey,

                'response' => $response->json(),
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'posts' => $data['history'] ?? [],
                    'total' => $data['count'] ?? 0,
                    'history' => $data['history'] ?? [],
                    'count' => $data['count'] ?? 0,
                    'refId' => $data['refId'] ?? null,
                    'lastUpdated' => $data['lastUpdated'] ?? null,
                    'nextUpdate' => $data['nextUpdate'] ?? null,
                    'current_page' => $page,
                    'per_page' => $perPage,
                ];
            }

            Log::error('Failed to get AyrShare posts with filters', [
                'userId' => $userId,
                'filters' => $filters,
                'statusCode' => $response->status(),
                'error' => $response->json(),
            ]);

            return [
                'posts' => [],
                'total' => 0,
                'history' => [],
                'count' => 0,
                'refId' => null,
                'lastUpdated' => null,
                'nextUpdate' => null,
                'current_page' => $page,
                'per_page' => $perPage,
            ];
        } catch (\Exception $e) {
            Log::error('Exception while getting AyrShare posts with filters', [
                'userId' => $userId,
                'filters' => $filters,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [
                'posts' => [],
                'total' => 0,
                'history' => [],
                'count' => 0,
                'refId' => null,
                'lastUpdated' => null,
                'nextUpdate' => null,
                'current_page' => $page,
                'per_page' => $perPage,
            ];
        }
    }

    /**
     * Schedule a post on AyrShare.
     */
    public function schedulePost(array $postData): bool
    {
        try {
            $result = $this->publishPost($postData);

            return is_array($result) && isset($result['status']) && $result['status'] === 'success';
        } catch (\Exception $e) {
            Log::error('Exception while scheduling AyrShare post', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    /**
     * Get analytics for a post.
     */
    public function getAnalytics(string $postId): array
    {
        try {
            return [];
        } catch (\Exception $e) {
            Log::error('Exception while getting AyrShare post analytics', [
                'postId' => $postId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return [];
        }
    }

    /**
     * Get the social media URL for a user
     *
     * @param  string|null  $token  JWT token
     * @param  array  $options  Additional options for URL generation
     * @return string|null URL or null on failure
     */
    public function getSocialUrl(?string $token = null, array $options = []): ?string
    {
        try {
            if (! $token) {
                Log::error('No JWT token provided for AyrShare social URL');

                return null;
            }

            $domainId = Config::get('socialmedia.connections.ayrshare.domain');

            if (! $domainId) {
                Log::error('AyrShare domain ID not configured');

                return null;
            }

            $url = "https://profile.ayrshare.com?domain={$domainId}&jwt={$token}";

            if (isset($options['redirect'])) {
                $url .= '&redirect='.urlencode($options['redirect']);
            }

            if (isset($options['logout']) && $options['logout']) {
                $url .= '&logout=true';
            }

            return $url;
        } catch (\Exception $e) {
            Log::error('Error generating AyrShare social URL', [
                'token' => $token ? 'present' : 'null',
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Generate JWT token and social URL for a user profile
     *
     * @param  string  $profileKey  The user's profile key
     * @param  array  $options  Additional options (redirect, logout, etc.)
     * @return array|null Array with token and url or null on failure
     */
    public function generateJwtAndUrl(string $profileKey, array $options = []): ?array
    {
        try {
            // Get required configuration values
            $domain = Config::get('socialmedia.connections.ayrshare.domain');
            $privateKey = Config::get('socialmedia.connections.ayrshare.private_key');

            if (! $domain || ! $privateKey) {
                Log::error('AyrShare domain or private key not configured', [
                    'profileKey' => $profileKey,
                    'domain_configured' => ! empty($domain),
                    'private_key_configured' => ! empty($privateKey),
                ]);

                return null;
            }

            $data = array_merge([
                'domain' => $domain,
                'privateKey' => $privateKey,
                'profileKey' => $profileKey,
            ], $options);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($this->baseUrl.'/profiles/generateJWT', $data);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['token']) && isset($data['url'])) {
                    return [
                        'token' => $data['token'],
                        'url' => $data['url'],
                    ];
                }

                if (isset($data['token'])) {
                    return [
                        'token' => $data['token'],
                        'url' => $this->getSocialUrl($data['token'], $options),
                    ];
                }

                Log::error('AyrShare JWT generation response has unexpected format', [
                    'profileKey' => $profileKey,
                    'response' => $data,
                ]);

                return null;
            }

            $errorData = $response->json();
            $statusCode = $response->status();

            Log::error('Failed to generate AyrShare JWT and URL', [
                'profileKey' => $profileKey,
                'statusCode' => $statusCode,
                'error' => $errorData,
            ]);

            return null;
        } catch (\Exception $e) {
            Log::error('Exception while generating AyrShare JWT and URL', [
                'profileKey' => $profileKey,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    /**
     * Delete a post on the AyrShare platform.
     *
     * @param  string  $postId  The ID of the post to delete
     * @return bool True if successfully deleted, false otherwise
     */
    public function deletePost(string $postId): bool
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->delete($this->baseUrl.'/post', [
                'id' => $postId,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                if (isset($data['status']) && $data['status'] === 'success') {
                    Log::info('Successfully deleted post from Ayrshare', [
                        'postId' => $postId,
                        'response' => $data,
                    ]);

                    return true;
                }

                Log::warning('Partial success deleting post from Ayrshare', [
                    'postId' => $postId,
                    'response' => $data,
                ]);

                return true;
            }

            $errorData = $response->json();

            Log::error('Failed to delete post from Ayrshare', [
                'postId' => $postId,
                'statusCode' => $response->status(),
                'error' => $errorData,
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Exception while deleting post from Ayrshare', [
                'postId' => $postId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }
    }

    public function uploadMedia($file, ?string $fileName = null, ?string $description = null, ?string $profileKey = null): array
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            if (is_string($file) && str_starts_with($file, 'data:')) {
                $data = ['file' => $file];
                if ($fileName) {
                    $data['fileName'] = $fileName;
                }
                if ($description) {
                    $data['description'] = $description;
                }

                $response = Http::timeout(60)
                    ->withHeaders(array_merge($headers, ['Content-Type' => 'application/json']))
                    ->post($this->baseUrl.'/media/upload', $data);

            } else {
                $response = $this->uploadFileWithMultipart($file, $fileName, $description, $headers);
            }

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'media_id' => $data['id'] ?? $data['mediaId'] ?? null,
                    'media_url' => $data['url'] ?? $data['mediaUrl'] ?? null,
                    'file_name' => $fileName,
                    'file_size' => $data['size'] ?? null,
                    'mime_type' => $data['type'] ?? $data['mimeType'] ?? null,
                    'raw_response' => $data,
                ];
            }

            $statusCode = $response->status();
            $errorData = $response->json();

            if ($statusCode === 401) {
                throw new \Exception('Unauthorized: Invalid API key or expired token');
            }

            if ($statusCode === 403) {
                throw new \Exception('Forbidden: User not connected to social media platform');
            }

            if ($statusCode === 404) {
                throw new \Exception('Profile not found: User profile does not exist');
            }

            Log::error('Failed to upload media to AyrShare', [
                'url' => $this->baseUrl.'/media/upload',
                'data' => $data,
                'fileName' => $fileName,
                'statusCode' => $statusCode,
                'error' => $errorData,
            ]);

            throw new \Exception('Failed to upload media: HTTP '.$statusCode);
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Unauthorized') === 0 ||
                strpos($e->getMessage(), 'Forbidden') === 0 ||
                strpos($e->getMessage(), 'Profile not found') === 0) {
                throw $e;
            }

            Log::error('Exception while uploading media to AyrShare', [
                'fileName' => $fileName,
                'error' => $e->getMessage(),
            ]);

            throw new \Exception('Failed to upload media: '.$e->getMessage());
        }
    }

    public function getUploadUrl(string $fileName, ?string $contentType = null, ?string $profileKey = null): array
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $params = ['fileName' => $fileName];
            if ($contentType) {
                $params['contentType'] = $contentType;
            }

            $response = Http::withHeaders($headers)->get($this->baseUrl.'/media/uploadUrl', $params);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'upload_url' => $data['uploadUrl'] ?? $data['url'] ?? null,
                    'media_id' => $data['id'] ?? $data['mediaId'] ?? null,
                    'expires_at' => $data['expiresAt'] ?? null,
                    'raw_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to get upload URL',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Exception while getting upload URL from AyrShare', [
                'fileName' => $fileName,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function getMediaGallery(?string $profileKey = null): array
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $response = Http::withHeaders($headers)->get($this->baseUrl.'/media');

            if ($response->successful()) {
                $data = $response->json();
                $mediaItems = $data['media'] ?? $data ?? [];

                $normalizedMedia = [];
                foreach ($mediaItems as $item) {
                    $normalizedMedia[] = [
                        'media_id' => $item['id'] ?? $item['mediaId'] ?? null,
                        'media_url' => $item['url'] ?? $item['mediaUrl'] ?? null,
                        'file_name' => $item['fileName'] ?? $item['name'] ?? null,
                        'file_size' => $item['size'] ?? null,
                        'mime_type' => $item['type'] ?? $item['mimeType'] ?? null,
                        'created_at' => $item['createdAt'] ?? $item['created'] ?? null,
                    ];
                }

                return [
                    'success' => true,
                    'media' => $normalizedMedia,
                    'total_count' => count($normalizedMedia),
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to get media gallery',
                'media' => [],
                'total_count' => 0,
            ];
        } catch (\Exception $e) {
            Log::error('Exception while getting media gallery from AyrShare', [
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'media' => [],
                'total_count' => 0,
            ];
        }
    }

    public function resizeMedia(string $mediaId, int $width, int $height, ?string $profileKey = null): array
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $data = [
                'url' => $mediaId,
                'width' => $width,
                'height' => $height,
            ];

            $response = Http::withHeaders($headers)->post($this->baseUrl.'/media/resize', $data);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'media_id' => $data['id'] ?? $data['mediaId'] ?? null,
                    'media_url' => $data['url'] ?? $data['mediaUrl'] ?? null,
                    'width' => $width,
                    'height' => $height,
                    'raw_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Failed to resize media',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Exception while resizing media on AyrShare', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function getMediaMetadata(string $mediaId, ?string $profileKey = null): array
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $response = Http::withHeaders($headers)->get($this->baseUrl.'/media/meta', [
                'url' => $mediaId,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'media_id' => $mediaId,
                    'file_name' => $data['fileName'] ?? $data['name'] ?? null,
                    'file_size' => $data['size'] ?? null,
                    'mime_type' => $data['type'] ?? $data['mimeType'] ?? null,
                    'width' => $data['width'] ?? null,
                    'height' => $data['height'] ?? null,
                    'duration' => $data['duration'] ?? null,
                    'created_at' => $data['createdAt'] ?? $data['created'] ?? null,
                    'raw_response' => $data,
                ];
            }

            return [
                'success' => false,
                'error' => 'Media not found',
                'status_code' => $response->status(),
            ];
        } catch (\Exception $e) {
            Log::error('Exception while getting media metadata from AyrShare', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    public function verifyMediaUrl(string $mediaUrl, ?string $profileKey = null): bool
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $response = Http::withHeaders($headers)->post($this->baseUrl.'/media/verify', [
                'url' => $mediaUrl,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return isset($data['exists']) ? (bool) $data['exists'] : true;
            }

            return false;
        } catch (\Exception $e) {
            Log::error('Exception while verifying media URL on AyrShare', [
                'mediaUrl' => $mediaUrl,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    public function deleteMedia(string $mediaId, ?string $profileKey = null): bool
    {
        try {
            $headers = [
                'Authorization' => 'Bearer '.$this->apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ];

            if ($profileKey) {
                $headers['Profile-Key'] = $profileKey;
            }

            $response = Http::withHeaders($headers)->delete($this->baseUrl.'/media', [
                'id' => $mediaId,
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return isset($data['status']) && $data['status'] === 'success';
            }

            Log::error('Failed to delete media from AyrShare', [
                'mediaId' => $mediaId,
                'statusCode' => $response->status(),
                'error' => $response->json(),
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('Exception while deleting media from AyrShare', [
                'mediaId' => $mediaId,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    private function uploadFileWithMultipart($file, ?string $fileName, ?string $description, array $headers): object
    {
        $postFields = $this->buildMultipartFields($file, $fileName, $description);
        $curlHeaders = $this->buildCurlHeaders($headers);

        $curlHandle = $this->initializeCurlHandle($postFields, $curlHeaders);

        [$responseBody, $httpCode] = $this->executeCurlRequest($curlHandle);

        return $this->createResponseObject($responseBody, $httpCode);
    }

    private function buildMultipartFields($file, ?string $fileName, ?string $description): array
    {
        $fields = [];

        if ($file instanceof \Illuminate\Http\UploadedFile) {
            $fields['file'] = new \CURLFile(
                $file->getRealPath(),
                $file->getMimeType(),
                $file->getClientOriginalName()
            );
        } else {
            $fields['file'] = $file;
        }

        if ($fileName) {
            $fields['fileName'] = $fileName;
        }

        if ($description) {
            $fields['description'] = $description;
        }

        return $fields;
    }

    private function buildCurlHeaders(array $headers): array
    {
        return array_map(
            fn ($key, $value) => "{$key}: {$value}",
            array_keys($headers),
            $headers
        );
    }

    private function initializeCurlHandle(array $postFields, array $curlHeaders)
    {
        $ch = curl_init();

        curl_setopt_array($ch, [
            CURLOPT_URL => $this->baseUrl.'/media/upload',
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $postFields,
            CURLOPT_HTTPHEADER => $curlHeaders,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 60,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => true,
        ]);

        return $ch;
    }

    private function executeCurlRequest($curlHandle): array
    {
        $responseBody = curl_exec($curlHandle);
        $httpCode = curl_getinfo($curlHandle, CURLINFO_HTTP_CODE);
        $curlError = curl_error($curlHandle);

        curl_close($curlHandle);

        if ($curlError) {
            throw new \Exception("cURL error: {$curlError}");
        }

        return [$responseBody, $httpCode];
    }

    private function createResponseObject(string $responseBody, int $httpCode): object
    {
        return new class($responseBody, $httpCode)
        {
            private string $body;

            private int $status;

            public function __construct(string $body, int $status)
            {
                $this->body = $body;
                $this->status = $status;
            }

            public function successful(): bool
            {
                return $this->status >= 200 && $this->status < 300;
            }

            public function json(): ?array
            {
                return json_decode($this->body, true);
            }

            public function status(): int
            {
                return $this->status;
            }
        };
    }
}
