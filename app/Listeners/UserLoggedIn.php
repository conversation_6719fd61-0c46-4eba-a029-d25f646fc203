<?php

namespace App\Listeners;

use App\Events\RefreshSocialJwtTokenEvent;
use Illuminate\Auth\Events\Login;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class UserLoggedIn implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(Login $event): void
    {
        $user = $event->user;

        try {
            event(new RefreshSocialJwtTokenEvent($user));
        } catch (\Exception $e) {
            Log::error('Failed to trigger JWT token refresh on login', [
                'user_id' => $user->id,
                'exception' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(Login $event, \Throwable $exception): void
    {
        Log::error('Failed to handle user login event', [
            'user_id' => $event->user->id,
            'exception' => $exception->getMessage(),
        ]);
    }
}
