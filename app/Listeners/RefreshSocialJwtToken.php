<?php

namespace App\Listeners;

use App\Enums\SocialPlatformType;
use App\Events\RefreshSocialJwtTokenEvent;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class RefreshSocialJwtToken implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     */
    public function handle(RefreshSocialJwtTokenEvent $event): void
    {
        $user = $event->user;
        $platform = config('socialmedia.default');

        try {

            $socialAccount = $user->socialAccounts()
                ->where('platform', $platform)
                ->where('is_active', true)
                ->first();

            if (! $socialAccount) {

                try {
                    $platformEnum = SocialPlatformType::from($platform);
                    $socialAccount = socialMediaService()->createUserSocialAccount(
                        $user,
                        $platformEnum,
                        []
                    );

                    if (! $socialAccount) {
                        Log::error('Failed to create social account for user', [
                            'user_id' => $user->id,
                            'platform' => $platform,
                        ]);

                        return;
                    }

                } catch (\Exception $e) {
                    Log::error('Failed to create social account for user', [
                        'user_id' => $user->id,
                        'platform' => $platform,
                        'exception' => $e->getMessage(),
                    ]);

                    return;
                }
            }

            if (! $socialAccount->platform_user_id) {
                return;
            }

            if ($socialAccount->access_token &&
                $socialAccount->token_expires_at &&
                $socialAccount->token_expires_at->isFuture()) {
                return;
            }

            $token = socialMediaService()->generateToken($user, $platform);

            if ($token) {
                $socialAccount->access_token = $token;
                $socialAccount->token_expires_at = now()->addMinutes(config('socialmedia.token_expiration.'.$platform));
                $socialAccount->save();

            }

        } catch (\Exception $e) {
            Log::error('Failed to refresh JWT token', [
                'user_id' => $user->id,
                'platform' => $platform,
                'exception' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(RefreshSocialJwtTokenEvent $event, \Throwable $exception): void
    {
        Log::error('Failed to refresh JWT token for user', [
            'user_id' => $event->user->id,
            'platform' => $event->platform,
            'exception' => $exception->getMessage(),
        ]);
    }
}
