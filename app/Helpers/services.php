<?php

use App\Contracts\Services\MediaServiceInterface;
use App\Contracts\Services\PromptServiceInterface;
use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Contracts\Services\UserSocialAccountServiceInterface;
use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Models\RagDocument;
use App\Models\User;
use App\Models\UserRagAccount;

if (! function_exists('socialMediaService')) {
    /**
     * Get the social media service instance.
     */
    function socialMediaService(): SocialMediaServiceInterface
    {
        return app(SocialMediaServiceInterface::class);
    }
}

if (! function_exists('userSocialAccountService')) {
    /**
     * Get the user social account service instance.
     */
    function userSocialAccountService(): UserSocialAccountServiceInterface
    {
        return app(UserSocialAccountServiceInterface::class);
    }
}

if (! function_exists('ragService')) {
    /**
     * Get the RAG service instance.
     */
    function ragService(): RagServiceInterface
    {
        return app(RagServiceInterface::class);
    }
}

if (! function_exists('userRagAccountService')) {
    /**
     * Get the user RAG account service instance.
     */
    function userRagAccountService(): UserRagAccountServiceInterface
    {
        return app(UserRagAccountServiceInterface::class);
    }
}

if (! function_exists('promptService')) {
    /**
     * Get the prompt service instance.
     */
    function promptService(): PromptServiceInterface
    {
        return app(PromptServiceInterface::class);
    }
}

if (! function_exists('getUserRagAccount')) {
    /**
     * Get a user's RAG account for a provider.
     */
    function getUserRagAccount(User $user, ?string $provider = null): ?UserRagAccount
    {
        return userRagAccountService()->getAccount($user, $provider);
    }
}

if (! function_exists('addRagDocument')) {
    /**
     * Add a document to a user's RAG account.
     */
    function addRagDocument(
        UserRagAccount $account,
        string $filePath,
        string $fileName,
        array $metadata = []
    ): ?RagDocument {
        return userRagAccountService()->addDocument($account, $filePath, $fileName, $metadata);
    }
}

if (! function_exists('askRag')) {
    /**
     * Ask a question using the RAG service.
     */
    function askRag(
        User $user,
        string $question,
        ?string $documentId = null,
        array $historyMessages = [],
        ?string $provider = null
    ): array {
        return ragService()->ask($user, $question, $documentId, $historyMessages, $provider);
    }
}

if (! function_exists('getPromptText')) {
    /**
     * Get the prompt text for a given prompt type and social platform.
     */
    function getPromptText(PromptType $type, SocialPlatform $platform): ?string
    {
        return promptService()->getPromptText($type, $platform);
    }
}

if (! function_exists('mediaService')) {
    /**
     * Get the media service instance.
     */
    function mediaService(): MediaServiceInterface
    {
        return app(MediaServiceInterface::class);
    }
}
