<?php

namespace App\Jobs;

use App\Contracts\Services\RagServiceInterface;
use App\Models\RagDocument;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ProcessRagDocumentUpload implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 300;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The RAG document instance.
     *
     * @var RagDocument
     */
    protected $document;

    /**
     * Create a new job instance.
     */
    public function __construct(RagDocument $document)
    {
        $this->document = $document;
    }

    /**
     * Execute the job.
     */
    public function handle(RagServiceInterface $ragService): void
    {
        // Skip if the document is already processed or failed
        if (in_array($this->document->status, ['completed', 'failed'])) {
            return;
        }

        $userRagAccount = $this->document->ragAccount;

        if (! $userRagAccount || ! $userRagAccount->is_active) {
            $this->document->update(['status' => 'failed']);
            Log::error('RAG document upload failed: Invalid RAG account', [
                'document_id' => $this->document->id,
            ]);

            return;
        }

        $adapter = $ragService->getAdapter($userRagAccount->provider);

        if (! $adapter) {
            $this->document->update(['status' => 'failed']);
            Log::error('RAG document upload failed: No adapter for provider', [
                'document_id' => $this->document->id,
                'provider' => $userRagAccount->provider,
            ]);

            return;
        }

        try {
            // Update status to processing
            $this->document->update(['status' => 'processing']);

            // Upload to the RAG service
            $result = $adapter->uploadDocument(
                $userRagAccount->collection_id,
                Storage::path($this->document->file_path),
                $this->document->original_filename
            );

            if (! $result) {
                $this->document->update(['status' => 'failed']);
                Log::error('RAG document upload failed: Service error', [
                    'document_id' => $this->document->id,
                ]);

                return;
            }

            // Update the document with the service document ID
            $this->document->update([
                'document_id' => $result['doc_name'] ?? $this->document->original_filename,
                'status' => 'completed',
            ]);

            // Get document outlines and FAQs
            try {
                $outlineFaq = $adapter->getDocumentOutlineFaq(
                    $userRagAccount->collection_id,
                    $this->document->document_id
                );

                $this->document->update([
                    'outlines' => $outlineFaq['outlines'] ?? [],
                    'faqs' => $outlineFaq['faqs'] ?? [],
                ]);
            } catch (\Exception $e) {
                Log::warning('Failed to get document outlines and FAQs', [
                    'error' => $e->getMessage(),
                    'document_id' => $this->document->id,
                ]);
                // Not marking as failed since the upload itself was successful
            }

        } catch (\Exception $e) {
            $this->document->update(['status' => 'failed']);
            Log::error('RAG document upload exception', [
                'error' => $e->getMessage(),
                'document_id' => $this->document->id,
            ]);
        }
    }
}
