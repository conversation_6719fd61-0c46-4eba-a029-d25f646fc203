<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Enums\SocialPlatform;
use App\Enums\SocialPlatformType;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class SocialAccountsRelationManager extends RelationManager
{
    protected static string $relationship = 'socialAccounts';

    /**
     * Get all available platform options combining both enums
     */
    private function getAllPlatformOptions(): array
    {
        $socialPlatforms = array_combine(
            SocialPlatform::values(),
            array_map(fn ($platform) => SocialPlatform::from($platform)->label(), SocialPlatform::values())
        );

        $platformTypes = array_combine(
            SocialPlatformType::values(),
            array_map(fn ($type) => SocialPlatformType::from($type)->label(), SocialPlatformType::values())
        );

        return array_merge($socialPlatforms, $platformTypes);
    }

    /**
     * Get the display label for a platform value
     */
    private function getPlatformLabel(string $platform): string
    {
        // Try SocialPlatform first
        try {
            return SocialPlatform::from($platform)->label();
        } catch (\ValueError) {
            // Fall back to SocialPlatformType
            try {
                return SocialPlatformType::from($platform)->label().' (Service)';
            } catch (\ValueError) {
                return $platform; // Return as-is if not found in either enum
            }
        }
    }

    /**
     * Get color for platform badge
     */
    private function getPlatformColor(string $platform): string
    {
        return match ($platform) {
            SocialPlatform::FACEBOOK->value => 'primary',
            SocialPlatform::INSTAGRAM->value => 'success',
            SocialPlatform::TWITTER->value => 'info',
            SocialPlatform::LINKEDIN->value => 'warning',
            SocialPlatform::YOUTUBE->value => 'danger',
            SocialPlatform::TIKTOK->value => 'pink',
            SocialPlatformType::SocialPilot->value => 'gray',
            SocialPlatformType::AyrShare->value => 'gray',
            default => 'secondary',
        };
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Account Information')
                    ->schema([
                        Forms\Components\Select::make('platform')
                            ->required()
                            ->options($this->getAllPlatformOptions())
                            ->searchable(),
                        Forms\Components\TextInput::make('username')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('platform_user_id')
                            ->label('Platform User ID')
                            ->maxLength(255),
                    ])
                    ->columns(3),

                Forms\Components\Section::make('Access Tokens')
                    ->schema([
                        Forms\Components\Textarea::make('access_token')
                            ->label('Access Token')
                            ->rows(3),
                        Forms\Components\Textarea::make('refresh_token')
                            ->label('Refresh Token')
                            ->rows(3),
                        Forms\Components\DateTimePicker::make('token_expires_at')
                            ->label('Token Expires At'),
                    ])
                    ->columns(1),

                Forms\Components\Section::make('Account Status')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Active')
                            ->default(true),
                        Forms\Components\Toggle::make('is_verified')
                            ->label('Verified')
                            ->default(false),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Additional Data')
                    ->schema([
                        Forms\Components\KeyValue::make('profile_data')
                            ->label('Profile Data')
                            ->keyLabel('Field')
                            ->valueLabel('Value'),
                        Forms\Components\KeyValue::make('account_stats')
                            ->label('Account Statistics')
                            ->keyLabel('Metric')
                            ->valueLabel('Value'),
                    ])
                    ->columns(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('platform')
            ->columns([
                Tables\Columns\TextColumn::make('platform')
                    ->formatStateUsing(fn (string $state): string => $this->getPlatformLabel($state))
                    ->badge()
                    ->color(fn (string $state): string => $this->getPlatformColor($state))
                    ->sortable(),

                Tables\Columns\TextColumn::make('username')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\IconColumn::make('is_active')
                    ->label('Active')
                    ->boolean(),

                Tables\Columns\IconColumn::make('is_verified')
                    ->label('Verified')
                    ->boolean(),

                Tables\Columns\TextColumn::make('token_expires_at')
                    ->label('Token Expires')
                    ->dateTime()
                    ->sortable()
                    ->color(fn ($state) => $state && $state < now() ? 'danger' : null)
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('platform')
                    ->options($this->getAllPlatformOptions())
                    ->multiple(),

                Tables\Filters\SelectFilter::make('platform_type')
                    ->label('Platform Type')
                    ->options([
                        'social' => 'Social Platforms',
                        'service' => 'Service Providers',
                    ])
                    ->query(function ($query, array $data) {
                        if (! $data['value']) {
                            return $query;
                        }

                        if ($data['value'] === 'social') {
                            return $query->whereIn('platform', SocialPlatform::values());
                        } elseif ($data['value'] === 'service') {
                            return $query->whereIn('platform', SocialPlatformType::values());
                        }

                        return $query;
                    }),

                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Active'),

                Tables\Filters\TernaryFilter::make('is_verified')
                    ->label('Verified'),

                Tables\Filters\Filter::make('token_expired')
                    ->label('Token Expired')
                    ->toggle()
                    ->query(fn ($query) => $query->where('token_expires_at', '<', now())),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }
}
