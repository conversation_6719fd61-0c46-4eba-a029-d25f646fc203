<?php

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Models\User;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;

class CompanyRelationManager extends RelationManager
{
    protected static string $relationship = 'company';

    public function getEloquentQuery(): Builder
    {
        $user = $this->ownerRecord;

        if ($user->user_type === User::TYPE_COMPANY_ADMIN) {
            return $user->company()->getQuery();
        } elseif ($user->user_type === User::TYPE_CONSUMER) {
            return $user->associatedCompany()->getQuery();
        }

        return parent::getEloquentQuery()->whereRaw('0 = 1');
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Company Information')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('legal_name')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('about')
                            ->columnSpanFull()
                            ->maxLength(300),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Address')
                    ->schema([
                        Forms\Components\TextInput::make('address')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('city')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('region')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('postcode')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Preferences')
                    ->schema([
                        Forms\Components\Textarea::make('language_preference')
                            ->rows(3),
                        Forms\Components\Textarea::make('tone_preference')
                            ->rows(3),
                        Forms\Components\TextInput::make('general_category')
                            ->maxLength(255),
                        Forms\Components\TextInput::make('sub_category')
                            ->maxLength(255),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Business Information')
                    ->schema([
                        Forms\Components\TextInput::make('vat')
                            ->label('VAT Number')
                            ->maxLength(255),
                    ])
                    ->columns(1),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('legal_name')
                    ->searchable()
                    ->toggleable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('relationship_type')
                    ->label('Relationship')
                    ->getStateUsing(function ($record) {
                        $user = $this->ownerRecord;
                        if ($user->user_type === User::TYPE_COMPANY_ADMIN && $user->company?->id === $record->id) {
                            return 'Owner';
                        } elseif ($user->user_type === User::TYPE_CONSUMER && $user->associatedCompany?->id === $record->id) {
                            return 'Member';
                        }

                        return 'Unknown';
                    })
                    ->colors([
                        'success' => 'Owner',
                        'warning' => 'Member',
                        'danger' => 'Unknown',
                    ]),
                Tables\Columns\TextColumn::make('city')
                    ->searchable()
                    ->toggleable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('language_preference')
                    ->toggleable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('tone_preference')
                    ->toggleable()
                    ->limit(50),
                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('language_preference')
                    ->options([
                        'en' => 'English',
                        'es' => 'Spanish',
                        'fr' => 'French',
                        'de' => 'German',
                    ]),
                Tables\Filters\SelectFilter::make('tone_preference')
                    ->options([
                        'professional' => 'Professional',
                        'friendly' => 'Friendly',
                        'casual' => 'Casual',
                        'formal' => 'Formal',
                    ]),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->visible(fn () => $this->ownerRecord->user_type === User::TYPE_COMPANY_ADMIN),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn () => $this->ownerRecord->user_type === User::TYPE_COMPANY_ADMIN),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->visible(fn () => $this->ownerRecord->user_type === User::TYPE_COMPANY_ADMIN),
                ]),
            ]);
    }
}
