<?php

namespace App\Filament\Resources;

use App\Enums\PromptType;
use App\Enums\SocialPlatform;
use App\Filament\Resources\PromptResource\Pages;
use App\Models\Prompt;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PromptResource extends Resource
{
    protected static ?string $model = Prompt::class;

    protected static ?string $navigationIcon = 'heroicon-o-chat-bubble-left-right';

    protected static ?string $navigationGroup = 'System';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255)
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn (Forms\Set $set, ?string $state) => $set('machine_name', \Illuminate\Support\Str::slug($state))),

                Forms\Components\TextInput::make('machine_name')
                    ->required()
                    ->maxLength(255)
                    ->disabled()
                    ->dehydrated(),

                Forms\Components\Select::make('type')
                    ->required()
                    ->options(array_combine(PromptType::values(), array_map(fn ($type) => PromptType::from($type)->label(), PromptType::values())))
                    ->live(),

                Forms\Components\Select::make('platform')
                    ->required()
                    ->options(array_combine(SocialPlatform::values(), array_map(fn ($platform) => SocialPlatform::from($platform)->label(), SocialPlatform::values())))
                    ->live(),

                Forms\Components\Textarea::make('prompt_text')
                    ->required()
                    ->rows(10)
                    ->columnSpanFull(),
            ])
            ->columns(2);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('machine_name')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('type')
                    ->formatStateUsing(fn ($state): string => $state?->label())
                    ->colors([
                        'primary' => PromptType::QUICK_POST->value,
                        'success' => PromptType::CAMPAIGN->value,
                    ]),

                Tables\Columns\TextColumn::make('platform')
                    ->formatStateUsing(fn ($state): string => $state?->label()),

                Tables\Columns\TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('type')
                    ->options(array_combine(PromptType::values(), array_map(fn ($type) => PromptType::from($type)->label(), PromptType::values()))),

                Tables\Filters\SelectFilter::make('platform')
                    ->options(array_combine(SocialPlatform::values(), array_map(fn ($platform) => SocialPlatform::from($platform)->label(), SocialPlatform::values()))),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPrompts::route('/'),
            'create' => Pages\CreatePrompt::route('/create'),
            'edit' => Pages\EditPrompt::route('/{record}/edit'),
        ];
    }
}
