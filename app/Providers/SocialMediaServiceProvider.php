<?php

namespace App\Providers;

use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\SocialMedia\SocialMediaAdapterInterface;
use App\Services\V1\SocialMedia\SocialMediaService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class SocialMediaServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(SocialMediaServiceInterface::class, function ($app) {
            $service = new SocialMediaService;
            $this->registerAdapters($service);

            return $service;
        });
    }


    /**
     * Register adapters with the service
     */
    protected function registerAdapters(SocialMediaService $service): void
    {
        $platforms = Config::get('socialmedia.connections', []);

        foreach ($platforms as $platform => $config) {
            if (isset($config['driver'])) {
                $adapterClass = $config['driver'];
                if (is_string($adapterClass) && class_exists($adapterClass)) {
                    try {

                        $adapter = new $adapterClass;

                        if ($adapter instanceof SocialMediaAdapterInterface) {
                            $service->registerAdapter($platform, $adapter);
                        }

                    } catch (\Exception $e) {
                        Log::error("Failed to instantiate adapter for platform: {$platform}", [
                            'error' => $e->getMessage(),
                            'adapter_class' => $adapterClass,
                        ]);
                    }
                }
            }
        }
    }
}
