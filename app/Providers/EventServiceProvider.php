<?php

namespace App\Providers;

use App\Events\CreateSocialMediaAccountEvent;
use App\Events\RefreshSocialJwtTokenEvent;
use App\Listeners\CreateSocialMediaAccount;
use App\Listeners\RefreshSocialJwtToken;
use App\Listeners\UserLoggedIn;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
        CreateSocialMediaAccountEvent::class => [
            CreateSocialMediaAccount::class,
        ],
        RefreshSocialJwtTokenEvent::class => [
            RefreshSocialJwtToken::class,
        ],
        Login::class => [
            UserLoggedIn::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
