<?php

namespace App\Providers;

use App\Contracts\RAG\RagServiceAdapterInterface;
use App\Contracts\Services\RagServiceInterface;
use App\Contracts\Services\UserRagAccountServiceInterface;
use App\Services\V1\RAG\RagService;
use App\Services\V1\RAG\UserRagAccountService;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;

class RagServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(RagServiceInterface::class, function ($app) {
            $service = new RagService;
            $this->registerAdapters($service);

            return $service;
        });

        $this->app->singleton(UserRagAccountServiceInterface::class, UserRagAccountService::class);
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Register adapters with the service
     */
    protected function registerAdapters(RagService $service): void
    {
        $providers = Config::get('rag.connections', []);

        foreach ($providers as $provider => $config) {
            if (isset($config['driver'])) {
                $adapterClass = $config['driver'];
                if (is_string($adapterClass) && class_exists($adapterClass)) {
                    try {
                        $adapter = new $adapterClass(
                            $config['api_key'] ?? '',
                            $config['account_id'] ?? '',
                            $config['base_url'] ?? null
                        );

                        if ($adapter instanceof RagServiceAdapterInterface) {
                            $service->registerAdapter($provider, $adapter);
                        }
                    } catch (\Exception $e) {
                        Log::error("Failed to instantiate adapter for provider: {$provider}", [
                            'error' => $e->getMessage(),
                            'adapter_class' => $adapterClass,
                        ]);
                    }
                }
            }
        }
    }
}
