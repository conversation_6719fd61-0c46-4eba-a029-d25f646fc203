<?php

namespace App\Providers;

use App\Contracts\Services\MediaGenerationServiceInterface;
use App\Contracts\Services\MediaServiceInterface;
use App\Contracts\Services\PromptServiceInterface;
use App\Contracts\Services\SocialMediaServiceInterface;
use App\Contracts\Services\UserSocialAccountServiceInterface;
use App\Services\V1\AIService\MediaGenerationService;
use App\Services\V1\MediaService;
use App\Services\V1\PromptService;
use App\Services\V1\SocialMedia\SocialMediaService;
use App\Services\V1\UserSocial\UserSocialAccountService;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->registerServices();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        $this->registerValidators();
    }

    /**
     * Register custom validators
     */
    private function registerValidators(): void
    {
        // Only register recaptcha if the class exists
        if (class_exists('\Biscolab\ReCaptcha\Rules\ReCaptchaRule')) {
            Validator::extend('recaptcha', '\Biscolab\ReCaptcha\Rules\ReCaptchaRule');
        }
    }

    /**
     * Register application services
     */
    private function registerServices(): void
    {
        $this->app->bind(UserSocialAccountServiceInterface::class, UserSocialAccountService::class);
        $this->app->bind(SocialMediaServiceInterface::class, SocialMediaService::class);
        $this->app->bind(MediaGenerationServiceInterface::class, MediaGenerationService::class);
        $this->app->bind(PromptServiceInterface::class, PromptService::class);
        $this->app->bind(MediaServiceInterface::class, MediaService::class);
    }
}
